import streamlit as st

# IMPORTANT: set_page_config MUST be the first Streamlit command
st.set_page_config(
    layout='wide',
    page_title="Chat - Chat with your data!",
    page_icon="💬"
)

# Now import everything else
import sys
import os
import logging
import traceback
import time
import uuid

# Add the parent directory to sys.path to import config and utils
from app_core.config import (
    init_session_state, init_llm, get_active_dataset, 
    get_dataset_sample
)
from app_core.utils.styling import apply_css
from app_core.utils.logging import log_message
from app_core.utils.visualization import display_result

# Initialize storage for logs to display in the UI
if "log_messages" not in st.session_state:
    st.session_state.log_messages = []

# Initialize session state if not already done
if 'has_data' not in st.session_state:
    init_session_state()

# Initialize context awareness toggle
if 'use_context' not in st.session_state:
    st.session_state.use_context = True

# Initialize visualization mode for datasets
if 'dataset_viz_mode' not in st.session_state:
    st.session_state.dataset_viz_mode = "table"  # Options: "table", "summary", "explorer"

# Initialize chat messages storage
if 'messages' not in st.session_state:
    st.session_state.messages = []

# Initialize dataset messages for multi-dataset conversations
if 'dataset_messages' not in st.session_state:
    st.session_state.dataset_messages = {}

# Apply centralized CSS
apply_css()

# Add data visibility controls and dataset selector to the sidebar  
with st.sidebar:
    # Add dataset selector if multiple datasets exist
    if len(st.session_state.lazy_datasets) > 0:
        st.subheader("Dataset Selection")

        # Initialize selected_datasets if not exists
        if "selected_datasets" not in st.session_state:
            st.session_state.selected_datasets = [st.session_state.active_dataset] if st.session_state.active_dataset else []

        dataset_options = list(st.session_state.lazy_datasets.keys())

        # Use checkboxes to select multiple datasets
        st.write("Select datasets to include in the conversation:")

        # Check if we need to select datasets by default
        if len(dataset_options) > 0 and len(st.session_state.selected_datasets) == 0:
            # Select the first dataset by default
            default_selection = [dataset_options[0]]
            st.session_state.selected_datasets = default_selection

        selected_datasets = []
        for dataset_name in dataset_options:
            # Check if this dataset was previously selected
            is_selected = dataset_name in st.session_state.selected_datasets

            # Get dataset info for display
            lazy_dataset = st.session_state.lazy_datasets[dataset_name]
            dataset_info = f"{dataset_name} ({lazy_dataset.shape[0]:,} rows)"

            # Create a checkbox for each dataset
            if st.checkbox(dataset_info, value=is_selected, key=f"checkbox_{dataset_name}"):
                selected_datasets.append(dataset_name)

        # Ensure at least one dataset is selected
        if not selected_datasets and dataset_options:
            selected_datasets = [dataset_options[0]]
            st.warning(f"At least one dataset must be selected. Using '{dataset_options[0]}'.")

        # Update selected datasets
        if selected_datasets != st.session_state.selected_datasets:
            st.session_state.selected_datasets = selected_datasets

            # Set the first selected dataset as the active dataset for backward compatibility
            if selected_datasets:
                st.session_state.active_dataset = selected_datasets[0]

            # Create a unique key for this combination of datasets
            multi_key = "+".join(sorted(selected_datasets))

            # Clear the chat messages for this combination
            st.session_state.messages = []
            if multi_key not in st.session_state.dataset_messages:
                st.session_state.dataset_messages[multi_key] = []

            # Clear conversation ID to start a new conversation
            if 'conversation_id' in st.session_state:
                del st.session_state.conversation_id

            st.rerun()        # Dataset preview section
        with st.popover("👁️ View Dataset", use_container_width=True):
            # If multiple datasets are selected, show a radio button to pick which one to display
            if len(st.session_state.selected_datasets) > 1:
                st.write("**Select dataset to preview:**")
                selected_to_view = st.radio(
                    "Choose a dataset:",
                    st.session_state.selected_datasets,
                    key="dataset_preview_toggle",
                    label_visibility="collapsed"
                )
            elif st.session_state.selected_datasets:
                selected_to_view = st.session_state.selected_datasets[0]
                st.write(f"**Previewing:** {selected_to_view}")
            else:
                selected_to_view = None

            if selected_to_view and selected_to_view in st.session_state.lazy_datasets:
                lazy_dataset = st.session_state.lazy_datasets[selected_to_view]
                
                # Show preview data using cached sample
                st.subheader("Data Preview")
                try:
                    preview_df = lazy_dataset.get_sample_cached(50)
                    st.dataframe(preview_df, use_container_width=True)
                except (KeyError, AttributeError) as e:
                    # Fallback to getting a fresh sample if cached sample is not available
                    st.info("Loading preview data...")
                    try:
                        preview_df = lazy_dataset.get_sample(50)
                        st.dataframe(preview_df, use_container_width=True)
                    except Exception as fallback_error:
                        st.error(f"Could not load preview data: {str(fallback_error)}")
            else:
                st.info("No active dataset selected")

    # Clear Conversation button - always visible when there are datasets
    st.markdown("---")  # Add a separator line
    if st.button("🗑️ Clear Conversation", help="Clear all chat messages and context", use_container_width=True, type="secondary"):
        # Clear the UI messages
        st.session_state.messages = []
        # Clear all conversation-related state
        if 'conversation_id' in st.session_state:
            del st.session_state.conversation_id
        if 'current_sdf' in st.session_state:
            del st.session_state.current_sdf
        if 'current_agent' in st.session_state:
            del st.session_state.current_agent
        st.rerun()

# Main chat interface
if not st.session_state.has_data or not st.session_state.lazy_datasets:
    # Enhanced empty state presentation similar to Data Explorer
    
    # Create centered layout with columns
    col1, col2, col3 = st.columns([1, 2, 1])
    
    with col2:
        # Main heading with icon
        st.markdown("""
        <div style="text-align: center; padding: 2rem 0;">
            <h1 style="color: #1f77b4; margin-bottom: 1rem;">
                💬 Chat with Your Data
            </h1>
            <p style="font-size: 1.2rem; color: #666; margin-bottom: 2rem;">
                Ask questions and get insights from your data using natural language
            </p>
        </div>
        """, unsafe_allow_html=True)
        
        # Getting started section
        st.markdown("""
        ### 🚀 Get Started
        
        To start chatting with your data, you'll need to upload at least one dataset first.
        """)
        
        # Action buttons
        st.markdown("<br>", unsafe_allow_html=True)
        
        # Create two columns for buttons
        btn_col1, btn_col2 = st.columns(2)
        
        with btn_col1:
            if st.button("📤 Upload Your Data", type="primary", use_container_width=True):
                st.switch_page("pages/1_Upload_Data.py")
        with btn_col2:
            if st.button("📥 Import from Oracle", type="primary", use_container_width=True):
                st.switch_page("pages/3_Database_Connections.py")

else:
    # Datasets are available - show the chat interface 
    
    st.header("Chat with your data")

    # Add a section for system prompt input in the sidebar
    with st.sidebar:
        st.header("System Prompt")

        # Initialize system prompt in session state if not already set
        if "system_prompt" not in st.session_state:
            st.session_state.system_prompt = ""

        # Text area for user to input or modify the system prompt
        st.session_state.system_prompt = st.text_area(
            "Enter or modify the system prompt:",
            value=st.session_state.system_prompt,
            placeholder="Type your system prompt here...",
            height=150
        )

        st.write("Current System Prompt:")
        st.code(st.session_state.system_prompt, language="plaintext")

    # Initialize context if not exists
    if "context" not in st.session_state:
        st.session_state.context = {}

    # Log the system prompt to verify it's being set
    logging.info(f"System prompt in UI: {st.session_state.system_prompt[:100] if st.session_state.system_prompt else 'EMPTY'}...")

    # Reinitialize the LLM if the system prompt changes
    if "last_system_prompt" not in st.session_state or st.session_state.last_system_prompt != st.session_state.system_prompt:
        st.session_state.last_system_prompt = st.session_state.system_prompt
        logging.info("System prompt changed, reinitializing LLM...")
        llm = init_llm()

    # Main chat interface - check for datasets using lazy_datasets
    if st.session_state.has_data and st.session_state.lazy_datasets:
        
        # Display chat history
        for message in st.session_state.messages:
            # Use custom avatar for user messages, default for assistant
            if message["role"] == "user":
                avatar = "🧑‍💻"  # User emoji
            else:
                avatar = "🤖"  # Assistant emoji

            with st.chat_message(message["role"], avatar=avatar):
                st.markdown(message["content"])

                # Display result visualization if available
                if "result" in message:
                    result = message["result"]
                    display_result(result)

                # Display code if available
                if "code" in message:
                    with st.expander("**📋 Generated Code**"):
                        st.code(message["code"], language="python")

    # Get user input
    if prompt := st.chat_input("Ask a question about your data..."):
        # Add user message to chat history
        st.session_state.messages.append({"role": "user", "content": prompt})        # Display user message with custom avatar
        with st.chat_message("user", avatar="🧑‍💻"):
            st.markdown(prompt)

        # Process with PandasAI and display response with custom avatar
        with st.chat_message("assistant", avatar="🤖"):
            with st.spinner("Analyzing your data..."):
                try:
                    # Make sure LLM is initialized before processing
                    llm = init_llm()

                    # Use standard PandasAI approach
                    log_message(f"Processing query: {prompt}")

                    # Get context setting from the toggle in the chat settings
                    use_context = st.session_state.use_context

                    # Import pandasai for direct access to chat and follow_up functions
                    import pandasai as pai

                    # Prepare datasets for processing
                    datasets_for_processing = []
                    
                    # Import SmartDataframe for proper PandasAI compatibility
                    from pandasai import SmartDataframe
                    
                    for dataset_name in st.session_state.selected_datasets:
                        lazy_dataset = st.session_state.lazy_datasets[dataset_name]
                        
                        # Check if we should use sampling
                        if hasattr(st.session_state, 'chat_sample_size') and st.session_state.chat_sample_size:
                            log_message(f"Using sample of {st.session_state.chat_sample_size} rows for {dataset_name}")
                            df_for_chat = lazy_dataset.sample(st.session_state.chat_sample_size)
                        else:
                            log_message(f"Loading full dataset for {dataset_name}")
                            df_for_chat = lazy_dataset.get_full_data()
                        
                        # Convert to SmartDataframe for PandasAI compatibility
                        smart_df = SmartDataframe(df_for_chat, name=dataset_name)
                        datasets_for_processing.append(smart_df)

                    # Process the query using PandasAI
                    if len(datasets_for_processing) > 1:
                        # Multiple datasets selected
                        log_message(f"Using multiple datasets: {len(datasets_for_processing)} datasets")

                        if not use_context or 'conversation_id' not in st.session_state or 'current_agent' not in st.session_state:
                            # Start a new conversation with multiple dataframes
                            log_message(f"Starting new conversation with multiple dataframes: {st.session_state.selected_datasets}")
                            result = pai.chat(prompt, *datasets_for_processing)

                            # Store the conversation ID and agent for future follow-ups
                            if hasattr(pai, "_current_agent") and pai._current_agent is not None:
                                st.session_state.conversation_id = id(pai._current_agent)
                                st.session_state.current_agent = pai._current_agent
                                log_message(f"Stored conversation ID: {st.session_state.conversation_id}")
                        else:
                            # Use follow_up for context-aware conversations
                            log_message(f"Following up in existing conversation with multiple dataframes")
                            try:
                                result = pai.follow_up(prompt)
                            except Exception as follow_up_error:
                                log_message(f"Follow-up failed, starting new conversation: {str(follow_up_error)}")
                                # Fallback to new conversation if follow_up fails
                                result = pai.chat(prompt, *datasets_for_processing)
                                if hasattr(pai, "_current_agent") and pai._current_agent is not None:
                                    st.session_state.conversation_id = id(pai._current_agent)
                                    st.session_state.current_agent = pai._current_agent
                    else:                        # Single dataset mode - use the SmartDataframe we already created
                        sdf_for_chat = datasets_for_processing[0]
                        
                        if not use_context or 'conversation_id' not in st.session_state or 'current_sdf' not in st.session_state:
                            # Start a new conversation
                            log_message(f"Starting new conversation with single dataframe: {st.session_state.active_dataset}")
                            
                            # Use the SmartDataframe we already created
                            result = sdf_for_chat.chat(prompt)
                            # Store the conversation ID for future follow-ups
                            if hasattr(sdf_for_chat, "_agent") and sdf_for_chat._agent is not None:
                                st.session_state.conversation_id = id(sdf_for_chat._agent)
                                st.session_state.current_sdf = sdf_for_chat  # Store for follow-ups
                                log_message(f"Stored conversation ID: {st.session_state.conversation_id}")
                        else:
                            # Use follow_up for context-aware conversations
                            log_message(f"Following up in existing conversation with single dataframe")
                            try:
                                if hasattr(st.session_state, 'current_sdf') and st.session_state.current_sdf is not None:
                                    result = st.session_state.current_sdf.follow_up(prompt)
                                else:
                                    raise Exception("No current SmartDataframe available")
                            except Exception as follow_up_error:
                                log_message(f"Follow-up failed, starting new conversation: {str(follow_up_error)}")
                                # Fallback to new conversation if sdf is lost or follow-up fails
                                # Use the SmartDataframe we already created
                                result = sdf_for_chat.chat(prompt)
                                st.session_state.current_sdf = sdf_for_chat
                                if hasattr(sdf_for_chat, "_agent") and sdf_for_chat._agent is not None:
                                    st.session_state.conversation_id = id(sdf_for_chat._agent)

                    # Flag to control whether to continue processing after special cases
                    continue_processing = True

                    # Special case for MultipleResponse - direct handling
                    if str(type(result)).find('MultipleResponse') != -1:
                        log_message("Direct handling of MultipleResponse object in chat")
                        # Let the display_result function handle it properly
                        display_result(result)
                        # Skip further processing to avoid double display
                        continue_processing = False

                    # Use the display_result function to handle all result types if not already handled
                    if continue_processing:
                        log_message("Using standard display_result function")
                        display_result(result)

                    # Get executed code if available
                    executed_code = None
                    if hasattr(result, "last_code_executed"):
                        executed_code = result.last_code_executed
                    elif hasattr(st.session_state, 'current_sdf') and hasattr(st.session_state.current_sdf, "_agent") and hasattr(st.session_state.current_sdf._agent, "last_code_executed"):
                        executed_code = st.session_state.current_sdf._agent.last_code_executed
                    elif hasattr(pai, "_current_agent") and hasattr(pai._current_agent, "last_code_executed"):
                        executed_code = pai._current_agent.last_code_executed

                    # Show code in expander if available (collapsed by default)
                    if executed_code:
                        st.markdown("---")
                        with st.expander("**📋 GENERATED CODE - Click to expand/collapse**", expanded=False):
                            st.code(executed_code, language="python")

                    # Add assistant response to chat history with result and code
                    if hasattr(result, 'description'):
                        response_text = result.description
                    elif isinstance(result, str):
                        response_text = result
                    else:
                        response_text = "Here are the results of your query:"

                    # For MultipleResponse objects, extract the value list
                    if str(type(result)).find('MultipleResponse') != -1 and hasattr(result, 'value'):
                        result_to_store = result.value
                    else:
                        result_to_store = result

                    message_data = {
                        "role": "assistant",
                        "content": response_text,
                        "result": result_to_store
                    }

                    # Add code to message history if available
                    if executed_code:
                        message_data["code"] = executed_code

                except Exception as e:
                    # Handle errors gracefully
                    error_message = f"Error processing your request: {str(e)}"
                    st.error(error_message)

                    # Log the error for debugging
                    logging.error(f"Error in chat processing: {str(e)}")
                    logging.error(traceback.format_exc())

                    message_data = {
                        "role": "assistant",
                        "content": error_message
                    }

                # Add the message to chat history
                st.session_state.messages.append(message_data)

