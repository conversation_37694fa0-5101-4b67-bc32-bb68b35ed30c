import io
import logging
from logging import <PERSON><PERSON><PERSON><PERSON>
import streamlit as st
import sys
import os
import glob
import traceback

# Set page configuration first before any other Streamlit command
st.set_page_config(
    layout='wide',
    page_title="View Logs",
    page_icon="💬"
)

# Add the parent directory to sys.path to import config
from app_core.config import init_session_state
from app_core.utils.styling import get_color, apply_css
from app_core.auth.auth_config import AuthConfig

# Set up a logger for this page
logger = logging.getLogger(__name__)

# Define the setup_log_capture function first
def setup_log_capture():
    """Set up log capture if not already in session state"""
    try:
        if "log_buffer" not in st.session_state:
            # Create an in-memory string buffer to capture logs
            st.session_state.log_buffer = io.StringIO()
            logger.info("Created new log buffer")

        if "log_capture" not in st.session_state:
            # Create a context-aware stream handler
            class ContextAwareStreamHandler(StreamHandler):
                def emit(self, record):
                    try:
                        # Check if we're in Streamlit context before emitting
                        if hasattr(st, '_get_context'):
                            context = st._get_context()
                            if context is not None:
                                super().emit(record)
                            # If no context, silently skip
                        else:
                            # Fallback - try to emit but catch any context errors
                            super().emit(record)
                    except Exception:
                        # Silent failure to avoid infinite loops
                        pass
            
            # Create a new handler with the existing buffer
            st.session_state.log_capture = ContextAwareStreamHandler(st.session_state.log_buffer)
            st.session_state.log_capture.setLevel(logging.DEBUG)

            # Format the logs
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            st.session_state.log_capture.setFormatter(formatter)

            # Add the handler to the root logger and pandasai logger specifically
            logging.getLogger().addHandler(st.session_state.log_capture)
            logging.getLogger('pandasai').addHandler(st.session_state.log_capture)
            logger.info("Log capture handler initialized")

        # Add a test log message to verify logging is working
        logging.info("Log viewer initialized")
        return True
    except Exception as e:
        st.error(f"Error setting up log capture: {str(e)}")
        logger.error(f"Error in setup_log_capture: {str(e)}")
        logger.error(traceback.format_exc())
        return False

def read_log_file():
    """Read the PandasAI log file if it exists"""
    try:
        # Look for both pandasai.log and our custom pandasai_app.log
        log_files = glob.glob("logs/pandasai*.log")
        logger.info(f"Found log files: {log_files}")

        if log_files:
            # Get the most recent log file
            log_file = max(log_files, key=os.path.getctime)
            logger.info(f"Reading from log file: {log_file}")
            try:
                with open(log_file, 'r', encoding='utf-8', errors='replace') as f:
                    content = f.read()
                    # Limit content to last 1000 lines to prevent UI slowdown
                    lines = content.splitlines()
                    if len(lines) > 1000:
                        content = "\n".join(lines[-1000:])
                        return f"Reading from {log_file} (last 1000 lines):\n\n{content}"
                    return f"Reading from {log_file}:\n\n{content}"
            except Exception as e:
                error_msg = f"Error reading log file: {str(e)}"
                logger.error(error_msg)
                logger.error(traceback.format_exc())
                return error_msg
        else:
            logger.warning("No log files found")
            return "No log files found. Try asking a question in the Chat page first."
    except Exception as e:
        error_msg = f"Error in read_log_file: {str(e)}"
        logger.error(error_msg)
        logger.error(traceback.format_exc())
        return error_msg

# Initialize session state if not already done
if 'has_data' not in st.session_state:
    init_session_state()

# Initialize authentication
auth = AuthConfig()

# Require authentication for this page
auth.require_authentication()

# We don't need to initialize LLM for the logs page
# This improves page load performance

# Initialize popover state if not exists
if 'show_data_popover' not in st.session_state:
    st.session_state.show_data_popover = False

# Apply the centralized CSS to ensure consistent styling
apply_css()

# Get theme colors for custom CSS
secondary_color = get_color('secondary')
secondary_hover_color = '#3a78f0'  # Slightly darker version of secondary

# Add custom CSS specific to this page
st.markdown(f"""
<style>
/* Floating scroll button styles */
.scroll-button-container button[data-testid="baseButton-secondary"] {{
    position: fixed !important;
    bottom: 20px !important;
    right: 20px !important;
    width: 50px !important;
    height: 50px !important;
    border-radius: 50% !important;
    background-color: {secondary_color} !important;
    color: white !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    cursor: pointer !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2) !important;
    z-index: 9999 !important;
    border: none !important;
    font-size: 24px !important;
    transition: all 0.3s ease !important;
    padding: 0 !important;
    min-width: unset !important;
}}

.scroll-button-container button[data-testid="baseButton-secondary"]:hover {{
    background-color: {secondary_hover_color} !important;
    transform: scale(1.05) !important;
}}

/* Add padding to the bottom of the page to prevent content from being hidden */
.main .block-container {{
    padding-bottom: 80px;
}}
</style>
""", unsafe_allow_html=True)

st.title("View Logs")

# Log capture is now set up in config.py's init_session_state function
# Just log that we're in the logs page
logger.info("View Logs page is active")

# Add a floating scroll button using Streamlit components
with st.container():
    # Add a class to the container for CSS targeting
    st.markdown('<div class="scroll-button-container">', unsafe_allow_html=True)

    # Use columns to position the button at the right side
    # The button will appear to float due to the CSS we added earlier
    col1, col2 = st.columns([9, 1])

    # Close the container div
    st.markdown('</div>', unsafe_allow_html=True)

# Create tabs for different log views
log_tab1, log_tab2 = st.tabs(["Session Logs", "Log Files"])

with log_tab1:
    # Create a container for in-memory logs
    st.subheader("Current Session Logs")

    # Display logs with refresh button
    if st.button("Refresh Session Logs", key="refresh_session"):
        logging.info("Session logs refreshed by user")
        # Just to trigger a refresh

    try:
        # Get in-memory logs
        if hasattr(st.session_state, 'log_buffer'):
            log_text = st.session_state.log_buffer.getvalue()
        else:
            log_text = ""
            st.warning("Log buffer not initialized properly. Try refreshing the page.")

        if not log_text:
            st.info("No session logs captured yet. Try asking a question in the Chat page.")
        else:
            # Display in a scrollable code block
            st.code(log_text, language="text")

        # Add options
        if log_text and st.button("Clear Session Logs", key="clear_session"):
            st.session_state.log_buffer.truncate(0)
            st.session_state.log_buffer.seek(0)
            logging.info("Session logs cleared by user")
            st.rerun()
    except Exception as e:
        st.error(f"Error displaying session logs: {str(e)}")
        logger.error(f"Error in session logs display: {str(e)}")
        logger.error(traceback.format_exc())

with log_tab2:
    st.subheader("Log Files")

    if st.button("Refresh Log Files", key="refresh_files"):
        logging.info("Log files refreshed by user")
        # Just to trigger a refresh

    # Read and display log file content
    log_file_content = read_log_file()

    if log_file_content:
        st.code(log_file_content, language="text")
    else:
        st.info("No log files found or unable to read log files.")
