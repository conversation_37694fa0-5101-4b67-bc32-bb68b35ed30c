# User Management Page
# Admin interface for managing users and their schema permissions

import streamlit as st
import sys
import os

# Page configuration
st.set_page_config(
    page_title="User Management",
    page_icon="👥",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Add the parent directory to the path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import required modules
from app_core.config import init_session_state
from app_core.auth.auth_config import AuthConfig
from app_core.utils.styling import apply_css
import pandas as pd

# Initialize session state
init_session_state()

# Apply styling
apply_css()

# Initialize authentication
auth = AuthConfig()

# Require authentication and admin permissions
auth.require_authentication()

if not auth.has_permission("admin"):
    st.error("🚫 Access denied. Admin permissions required.")
    st.stop()

# Render user info in sidebar
auth.render_user_info()

# Main page content
st.title("👥 User Management")
st.markdown("Manage users and their database schema permissions.")

# Create tabs for different management functions
tab1, tab2, tab3 = st.tabs(["👥 View Users", "➕ Add User", "⚙️ Manage Permissions"])

with tab1:
    st.header("Current Users")
    
    # Get all users
    users = auth.user_manager.get_all_users()
    roles = auth.user_manager.get_roles()
    
    if users:
        # Convert to DataFrame for display
        users_data = []
        for username, user_data in users.items():
            users_data.append({
                "Username": username,
                "Full Name": user_data["full_name"],
                "Email": user_data["email"],
                "Role": user_data["role"].title(),
                "Status": "Active" if user_data["active"] else "Inactive",
                "Schema Permissions": ", ".join(user_data["schema_permissions"]) if user_data["schema_permissions"] != ["*"] else "All Schemas"
            })
        
        users_df = pd.DataFrame(users_data)
        st.dataframe(users_df, use_container_width=True)
          # User actions
        st.subheader("User Actions")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.markdown("**Deactivate User**")
            active_users = [username for username, data in users.items() if data["active"]]
            user_to_deactivate = st.selectbox("Select user to deactivate:", [""] + active_users, key="deactivate_select")
            
            if st.button("🔒 Deactivate User", disabled=not user_to_deactivate):
                if auth.user_manager.deactivate_user(user_to_deactivate):
                    st.success(f"✅ User {user_to_deactivate} deactivated successfully!")
                    st.rerun()
                else:
                    st.error("❌ Failed to deactivate user.")
        
        with col2:
            st.markdown("**Remove User**")
            all_users_list = list(users.keys())
            user_to_remove = st.selectbox("Select user to remove:", [""] + all_users_list, key="remove_select")
            
            if user_to_remove:
                st.warning("⚠️ This will permanently delete the user!")
                confirm_removal = st.checkbox(f"I confirm removal of {user_to_remove}", key="confirm_remove")
                
                if st.button("🗑️ Remove User", disabled=not confirm_removal, type="secondary"):
                    if auth.user_manager.remove_user(user_to_remove):
                        st.success(f"✅ User {user_to_remove} removed successfully!")
                        st.rerun()
                    else:
                        st.error("❌ Failed to remove user.")
        
        with col3:
            st.markdown("**Reactivate User**")
            inactive_users = [username for username, data in users.items() if not data["active"]]
            user_to_reactivate = st.selectbox("Select user to reactivate:", [""] + inactive_users, key="reactivate_select")
            
            if st.button("🔓 Reactivate User", disabled=not user_to_reactivate):
                if auth.user_manager.activate_user(user_to_reactivate):
                    st.success(f"✅ User {user_to_reactivate} reactivated successfully!")
                    st.rerun()
                else:
                    st.error("❌ Failed to reactivate user.")
        
        # Role information section
        st.markdown("---")
        st.subheader("Role Information")
        selected_role = st.selectbox("View role details:", list(roles.keys()))
        if selected_role:
            role_info = roles[selected_role]
            st.info(f"**{selected_role.title()}**: {role_info['description']}")
            st.write(f"**Permissions**: {', '.join(role_info['permissions'])}")
    else:
        st.info("No users found.")

with tab2:
    st.header("Add New User")
    
    with st.form("add_user_form"):
        col1, col2 = st.columns(2)
        
        with col1:
            new_username = st.text_input("Username *", placeholder="Enter username")
            new_password = st.text_input("Password *", type="password", placeholder="Enter password")
            confirm_password = st.text_input("Confirm Password *", type="password", placeholder="Confirm password")
        
        with col2:
            new_full_name = st.text_input("Full Name *", placeholder="Enter full name")
            new_email = st.text_input("Email *", placeholder="Enter email address")
            new_role = st.selectbox("Role *", list(auth.user_manager.get_roles().keys()))
        
        st.markdown("**Schema Permissions**")
        st.markdown("Select the Oracle schemas this user can access:")
        
        # Schema permissions selection with actual Oracle schemas
        schema_options = ['AARO', 'ADMIN', 'APEX_240200', 'AUDSYS', 'AWF', 'BELLECHR', 'C##ADP$SERVICE', 
                         'C##CLOUD$SERVICE', 'C##OMLIDM', 'DARWINDWH', 'DATAMART_REP', 'DBSFWUSER', 
                         'DVSYS', 'DWH_PUBLIC', 'DWH_STAGING', 'DWH_TEST', 'DWH_WORK', 'EBS', 
                         'ELECTROLUX_DWH', 'ELECTROLUX_STG', 'ELECTROLUX_STG_EX', 'FINANCE', 'FIN_OP', 
                         'FMWREGISTRY', 'GGADMIN', 'GRAPH$METADATA', 'GSMADMIN_INTERNAL', 'JABEERAK', 
                         'KALUSABD1', 'LBACSYS', 'MTSSYS', 'ODIINT_IAU', 'ODIINT_OPSS', 'ODIINT_REPO', 
                         'ODIINT_REPO_DEV', 'ODIINT_REPO_EXE', 'ODIINT_STB', 'ODIINT_WLS', 
                         'ODIINT_WLS_RUNTIME', 'ODIINT_WORK', 'ODIOP_IAU', 'ODIOP_OPSS', 'ODIOP_PARAMS', 
                         'ODIOP_REPO', 'ODIOP_REPO_DEV', 'ODIOP_REPO_EXE', 'ODIOP_STB', 'ODIOP_WLS', 
                         'ODIOP_WLS_RUNTIME', 'ODIOP_WORK', 'ODIPRO_IAU', 'ODIPRO_ODI_REPO', 'ODIPRO_OPSS', 
                         'ODIPRO_STB', 'ODIPRO_WLS', 'ODIPRO_WLS_RUNTIME', 'ODIWEBE_ODI_REPO', 
                         'ODIWEB_ODI_REPO', 'ODI_REPO_USER', 'ODI_TEMP', 'OML$METADATA', 'OML$MODELS', 
                         'OML_ADMIN', 'OML_USER', 'ORDS_METADATA', 'OTC', 'OTC_ICEOP', 'PLYCTL', 'PLYDTA', 
                         'PRIDE', 'PRODCTL', 'PRODDTA', 'PTP', 'PYQSYS', 'REHMASAM', 'RMAN$CATALOG', 
                         'RQSYS', 'SERVICE', 'SH', 'SSB', 'UT', 'WINDCHILL', 'WKSP_JDEDEVELOPMENT', 
                         'WKSP_PURCHASING']
        
        # Display schemas in a more manageable way
        if st.checkbox("All Schemas", key="all_schemas_new"):
            selected_schemas = ["*"]
        else:
            selected_schemas = []
            
            # Use multiselect for better UX with many schemas
            st.markdown("**Select Schemas:**")
            selected_schemas = st.multiselect(
                "Choose schemas (you can search by typing):",
                options=schema_options,
                key="schema_multiselect_new",
                help="Start typing to search for specific schemas"
            )
            
            # Option to add custom schema
            custom_schema = st.text_input("Custom Schema", placeholder="Enter custom schema name", key="custom_schema_new")
            if custom_schema:
                selected_schemas.append(custom_schema.upper())
        
        submitted = st.form_submit_button("➕ Add User", type="primary")
        
        if submitted:
            # Validation
            if not all([new_username, new_password, new_full_name, new_email, new_role]):
                st.error("❌ Please fill in all required fields marked with *")
            elif new_password != confirm_password:
                st.error("❌ Passwords do not match")
            elif len(new_password) < 6:
                st.error("❌ Password must be at least 6 characters long")
            elif not selected_schemas:
                st.error("❌ Please select at least one schema permission")
            else:
                # Add user
                if auth.user_manager.add_user(
                    new_username, new_password, new_role, 
                    new_full_name, new_email, selected_schemas
                ):
                    st.success(f"✅ User {new_username} added successfully!")
                    st.info("The user can now log in with their credentials.")
                else:
                    st.error("❌ Failed to add user. Username may already exist.")

with tab3:
    st.header("Manage User Permissions")
    
    users = auth.user_manager.get_all_users()
    active_users = [username for username, data in users.items() if data["active"]]
    
    if active_users:
        selected_user = st.selectbox("Select user to modify:", [""] + active_users)
        
        if selected_user:
            user_data = users[selected_user]
            current_schemas = user_data["schema_permissions"]
            
            st.markdown(f"**Current permissions for {user_data['full_name']}:**")
            if "*" in current_schemas:
                st.info("🌟 All Schemas")
            else:
                st.info(f"📋 {', '.join(current_schemas)}")
            
            with st.form("update_permissions_form"):
                st.markdown("**Update Schema Permissions**")
                
                # Use the same Oracle schema list
                schema_options = ['AARO', 'ADMIN', 'APEX_240200', 'AUDSYS', 'AWF', 'BELLECHR', 'C##ADP$SERVICE', 
                                 'C##CLOUD$SERVICE', 'C##OMLIDM', 'DARWINDWH', 'DATAMART_REP', 'DBSFWUSER', 
                                 'DVSYS', 'DWH_PUBLIC', 'DWH_STAGING', 'DWH_TEST', 'DWH_WORK', 'EBS', 
                                 'ELECTROLUX_DWH', 'ELECTROLUX_STG', 'ELECTROLUX_STG_EX', 'FINANCE', 'FIN_OP', 
                                 'FMWREGISTRY', 'GGADMIN', 'GRAPH$METADATA', 'GSMADMIN_INTERNAL', 'JABEERAK', 
                                 'KALUSABD1', 'LBACSYS', 'MTSSYS', 'ODIINT_IAU', 'ODIINT_OPSS', 'ODIINT_REPO', 
                                 'ODIINT_REPO_DEV', 'ODIINT_REPO_EXE', 'ODIINT_STB', 'ODIINT_WLS', 
                                 'ODIINT_WLS_RUNTIME', 'ODIINT_WORK', 'ODIOP_IAU', 'ODIOP_OPSS', 'ODIOP_PARAMS', 
                                 'ODIOP_REPO', 'ODIOP_REPO_DEV', 'ODIOP_REPO_EXE', 'ODIOP_STB', 'ODIOP_WLS', 
                                 'ODIOP_WLS_RUNTIME', 'ODIOP_WORK', 'ODIPRO_IAU', 'ODIPRO_ODI_REPO', 'ODIPRO_OPSS', 
                                 'ODIPRO_STB', 'ODIPRO_WLS', 'ODIPRO_WLS_RUNTIME', 'ODIWEBE_ODI_REPO', 
                                 'ODIWEB_ODI_REPO', 'ODI_REPO_USER', 'ODI_TEMP', 'OML$METADATA', 'OML$MODELS', 
                                 'OML_ADMIN', 'OML_USER', 'ORDS_METADATA', 'OTC', 'OTC_ICEOP', 'PLYCTL', 'PLYDTA', 
                                 'PRIDE', 'PRODCTL', 'PRODDTA', 'PTP', 'PYQSYS', 'REHMASAM', 'RMAN$CATALOG', 
                                 'RQSYS', 'SERVICE', 'SH', 'SSB', 'UT', 'WINDCHILL', 'WKSP_JDEDEVELOPMENT', 
                                 'WKSP_PURCHASING']
                
                all_schemas_checked = "*" in current_schemas
                if st.checkbox("All Schemas", value=all_schemas_checked, key="all_schemas_update"):
                    new_selected_schemas = ["*"]
                else:
                    # Get currently selected schemas (excluding "*")
                    current_selected = [s for s in current_schemas if s != "*"]
                    
                    # Use multiselect for better UX
                    st.markdown("**Select Schemas:**")
                    new_selected_schemas = st.multiselect(
                        "Choose schemas (you can search by typing):",
                        options=schema_options,
                        default=[s for s in current_selected if s in schema_options],
                        key="schema_multiselect_update",
                        help="Start typing to search for specific schemas"
                    )
                    
                    # Show any custom schemas that are currently assigned but not in the main list
                    custom_schemas = [s for s in current_selected if s not in schema_options]
                    if custom_schemas:
                        st.markdown("**Current Custom Schemas:**")
                        for custom_schema in custom_schemas:
                            if st.checkbox(f"Keep {custom_schema}", value=True, key=f"keep_{custom_schema}"):
                                new_selected_schemas.append(custom_schema)
                    
                    # Option to add new custom schema
                    new_custom_schema = st.text_input("Add Custom Schema", placeholder="Enter custom schema name", key="new_custom_schema_update")
                    if new_custom_schema:
                        new_selected_schemas.append(new_custom_schema.upper())
                
                submitted = st.form_submit_button("🔄 Update Permissions", type="primary")
                
                if submitted:
                    if not new_selected_schemas:
                        st.error("❌ Please select at least one schema permission")
                    else:
                        if auth.user_manager.update_user_schemas(selected_user, new_selected_schemas):
                            st.success(f"✅ Permissions updated for {selected_user}!")
                            st.rerun()
                        else:
                            st.error("❌ Failed to update permissions")
    else:
        st.info("No active users found to manage.")
