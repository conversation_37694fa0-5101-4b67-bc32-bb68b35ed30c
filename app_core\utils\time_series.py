"""
Time Series Analysis and Forecasting utilities using Darts library.
This module provides functionality for time series analysis, forecasting, and visualization.
"""

import pandas as pd
import numpy as np
import streamlit as st
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import logging
from typing import Dict, List, Optional, Tuple, Union
from datetime import datetime, timedelta
import warnings

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)

# Try to import Darts components
try:
    from darts import TimeSeries
    from darts.models import (
        LinearRegressionModel, 
        ExponentialSmoothing, 
        AutoARIMA,
        Prophet,
        NaiveSeasonal,
        NaiveDrift,
        RandomForest,
        LightGBMModel
    )
    from darts.metrics import mape, smape, mae, mse, rmse, r2_score
    from darts.utils.statistics import check_seasonality, extract_trend_and_seasonality
    from darts.utils.utils import SeasonalityMode
    
    DARTS_AVAILABLE = True
    logger.info("Darts library loaded successfully")
except ImportError as e:
    DARTS_AVAILABLE = False
    logger.warning(f"Darts library not available: {e}")

class TimeSeriesAnalyzer:
    """Main class for time series analysis and forecasting"""
    
    def __init__(self):
        self.models = {}
        self.forecasts = {}
        self.metrics = {}
    
    def _safe_to_pandas(self, ts: TimeSeries) -> pd.DataFrame:
        """Safely convert Darts TimeSeries to pandas DataFrame, handling API differences"""
        try:
            # Try the newer method first (Darts >= 0.25.0)
            if hasattr(ts, 'to_pandas'):
                return ts.to_pandas()
            # Fall back to older method (Darts < 0.25.0)
            elif hasattr(ts, 'pd_dataframe'):
                return ts.pd_dataframe()
            else:
                raise AttributeError("TimeSeries object has no pandas conversion method")
        except Exception as e:
            logger.error(f"Error converting TimeSeries to pandas: {e}")
            raise
        
    def detect_time_columns(self, df: pd.DataFrame) -> List[str]:
        """Detect potential time/date columns in the dataframe"""
        time_columns = []
        
        for col in df.columns:
            col_lower = col.lower()
            
            # Check for obvious time column names
            time_keywords = ['date', 'time', 'timestamp', 'datetime', 'period', 'year', 'month', 'day']
            if any(keyword in col_lower for keyword in time_keywords):
                time_columns.append(col)
                continue
                
            # Check data type
            if pd.api.types.is_datetime64_any_dtype(df[col]):
                time_columns.append(col)
                continue
                
            # Try to parse as datetime
            try:
                sample_values = df[col].dropna().head(10).astype(str)
                if len(sample_values) > 0:
                    pd.to_datetime(sample_values, errors='raise')
                    time_columns.append(col)
            except:
                pass
                
        return time_columns
        
    def detect_numeric_columns(self, df: pd.DataFrame) -> List[str]:
        """Detect numeric columns suitable for time series analysis"""
        numeric_columns = []
        
        for col in df.columns:
            if pd.api.types.is_numeric_dtype(df[col]):
                # Check if it has reasonable variation (not all same values)
                if df[col].nunique() > 1:
                    numeric_columns.append(col)
                    
        return numeric_columns
        
    def preprocess_data(self, df: pd.DataFrame, time_col: str, value_col: str) -> pd.DataFrame:
        """Preprocess data for time series analysis"""
        try:
            # Create a copy to avoid modifying original data
            ts_df = df[[time_col, value_col]].copy()
            
            # Convert time column to datetime
            ts_df[time_col] = pd.to_datetime(ts_df[time_col])
            
            # Remove duplicates and sort by time
            ts_df = ts_df.drop_duplicates(subset=[time_col]).sort_values(time_col)
            
            # Handle missing values
            ts_df = ts_df.dropna()
              # Reset index
            ts_df = ts_df.reset_index(drop=True)
            
            return ts_df
            
        except Exception as e:
            logger.error(f"Error preprocessing data: {e}")
            raise
    
    def _fix_time_series_frequency(self, df: pd.DataFrame, time_col: str, value_col: str) -> pd.DataFrame:
        """Fix frequency issues in time series data"""
        try:
            # Sort by time
            df = df.sort_values(time_col).reset_index(drop=True)
            
            # Remove exact duplicates
            df = df.drop_duplicates(subset=[time_col])
            
            # Check for very small time differences that might cause issues
            time_diffs = df[time_col].diff().dropna()
            if len(time_diffs) > 0:
                min_diff = time_diffs.min()
                if min_diff.total_seconds() < 1:  # Less than 1 second difference
                    logger.warning("Found very small time differences, aggregating by minute")
                    df[time_col] = df[time_col].dt.floor('T')  # Floor to minute
                    df = df.groupby(time_col)[value_col].mean().reset_index()
            
            return df
            
        except Exception as e:
            logger.warning(f"Error fixing frequency, using original data: {e}")
            return df
    
    def _infer_frequency(self, time_series: pd.Series) -> Optional[str]:
        """Manually infer frequency from time series"""
        try:
            if len(time_series) < 2:
                return None
                
            # Calculate time differences
            time_diffs = time_series.diff().dropna()
            
            if len(time_diffs) == 0:
                return None
            
            # Find the most common time difference
            most_common_diff = time_diffs.mode()
            if len(most_common_diff) == 0:
                return None
                
            common_diff = most_common_diff.iloc[0]
            
            # Map common differences to pandas frequency strings
            total_seconds = common_diff.total_seconds()
            
            if total_seconds == 86400:  # 1 day
                return 'D'
            elif total_seconds == 3600:  # 1 hour
                return 'H'
            elif total_seconds == 60:  # 1 minute
                return 'T'
            elif total_seconds == 1:  # 1 second
                return 'S'
            elif total_seconds == 604800:  # 1 week
                return 'W'
            elif 2419200 <= total_seconds <= 2678400:  # ~1 month (28-31 days)
                return 'M'
            else:
                # For irregular frequencies, return None
                return None
                
        except Exception as e:
            logger.warning(f"Error inferring frequency: {e}")
            return None
    
    def _create_regular_timeseries(self, df: pd.DataFrame, time_col: str, value_col: str) -> TimeSeries:
        """Create a regular time series by resampling irregular data"""
        try:
            logger.info("Creating regular time series through resampling")
            
            # Set time column as index
            df_indexed = df.set_index(time_col)
            
            # Determine appropriate resampling frequency
            time_range = df_indexed.index.max() - df_indexed.index.min()
            num_points = len(df_indexed)
            
            if time_range.days > 365 * 2:  # More than 2 years
                freq = 'M'  # Monthly
            elif time_range.days > 90:  # More than 3 months
                freq = 'W'  # Weekly
            elif time_range.days > 7:  # More than 1 week
                freq = 'D'  # Daily
            else:
                freq = 'H'  # Hourly
            
            # Resample the data
            resampled = df_indexed[value_col].resample(freq).mean().dropna()
            
            if len(resampled) < 2:
                raise ValueError("Not enough data points after resampling")
            
            # Create DataFrame for Darts
            resampled_df = resampled.reset_index()
            
            # Create TimeSeries with explicit frequency
            ts = TimeSeries.from_dataframe(
                resampled_df,
                time_col=time_col,
                value_cols=[value_col],
                freq=freq            )
            
            logger.info(f"Successfully created regular TimeSeries with frequency: {freq}")
            return ts
            
        except Exception as e:
            logger.error(f"Error creating regular time series: {e}")
            raise
            
    def create_darts_timeseries(self, df: pd.DataFrame, time_col: str, value_col: str) -> TimeSeries:
        """Create a Darts TimeSeries object from DataFrame with proper frequency handling"""
        if not DARTS_AVAILABLE:
            raise ImportError("Darts library is not available")
            
        try:
            # Preprocess the data
            ts_df = self.preprocess_data(df, time_col, value_col)
            
            # Try to infer and fix frequency issues
            ts_df = self._fix_time_series_frequency(ts_df, time_col, value_col)
            
            # Create Darts TimeSeries with frequency handling
            try:
                # First try without specifying frequency
                ts = TimeSeries.from_dataframe(
                    ts_df, 
                    time_col=time_col, 
                    value_cols=[value_col],
                    freq=None
                )
            except Exception as freq_error:
                logger.warning(f"Frequency inference failed: {freq_error}")
                
                # Try with fill_missing_dates=True
                try:
                    ts = TimeSeries.from_dataframe(
                        ts_df, 
                        time_col=time_col, 
                        value_cols=[value_col],
                        freq=None,
                        fill_missing_dates=True
                    )
                    logger.info("Successfully created TimeSeries with missing dates filled")
                except Exception as fill_error:
                    logger.warning(f"Fill missing dates failed: {fill_error}")
                    
                    # Try to infer frequency manually and use it
                    inferred_freq = self._infer_frequency(ts_df[time_col])
                    if inferred_freq:
                        try:
                            ts = TimeSeries.from_dataframe(
                                ts_df, 
                                time_col=time_col, 
                                value_cols=[value_col],
                                freq=inferred_freq,
                                fill_missing_dates=True
                            )
                            logger.info(f"Successfully created TimeSeries with inferred frequency: {inferred_freq}")
                        except Exception as manual_freq_error:
                            logger.warning(f"Manual frequency setting failed: {manual_freq_error}")
                            # Last resort: resample to regular frequency
                            ts = self._create_regular_timeseries(ts_df, time_col, value_col)
                    else:
                        # Last resort: resample to regular frequency
                        ts = self._create_regular_timeseries(ts_df, time_col, value_col)
            
            return ts
            
        except Exception as e:            
            logger.error(f"Error creating Darts TimeSeries: {e}")
            raise
            
    def analyze_time_series(self, ts: TimeSeries) -> Dict:
        """Analyze time series characteristics with error handling"""
        if not DARTS_AVAILABLE:
            raise ImportError("Darts library is not available")
            
        try:
            analysis = {
                'length': len(ts),
                'start_date': ts.start_time(),
                'end_date': ts.end_time(),
                'frequency': ts.freq_str if hasattr(ts, 'freq_str') and ts.freq_str else 'Irregular',
                'has_missing_values': False,  # We've already handled missing values
                'components': ts.components.tolist(),
                'values_min': float(ts.min().values()[0]),
                'values_max': float(ts.max().values()[0]),
                'values_mean': float(ts.mean().values()[0]),
                'values_std': float(ts.std().values()[0])
            }
            
            # Check for seasonality with error handling
            try:
                if len(ts) > 10:  # Need at least some data points
                    max_lag = min(len(ts) // 3, 365, len(ts) - 1)
                    if max_lag > 1:
                        seasonality_result = check_seasonality(ts, max_lag=max_lag)
                        analysis['seasonality'] = {
                            'is_seasonal': bool(seasonality_result),
                            'period': int(seasonality_result) if seasonality_result else None
                        }
                    else:
                        analysis['seasonality'] = {'is_seasonal': False, 'period': None}
                else:
                    analysis['seasonality'] = {'is_seasonal': False, 'period': None}
            except Exception as seasonality_error:
                logger.warning(f"Seasonality detection failed: {seasonality_error}")
                analysis['seasonality'] = {'is_seasonal': False, 'period': None}
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing time series: {e}")
            # Return basic analysis if detailed analysis fails
            return {
                'length': len(ts) if ts is not None else 0,
                'start_date': 'Unknown',
                'end_date': 'Unknown',
                'frequency': 'Unknown',
                'has_missing_values': False,
                'components': [],
                'values_min': 0,
                'values_max': 0,
                'values_mean': 0,
                'values_std': 0,
                'seasonality': {'is_seasonal': False, 'period': None}
            }
            
    def split_train_test(self, ts: TimeSeries, test_size: float = 0.2) -> Tuple[TimeSeries, TimeSeries]:
        """Split time series into train and test sets"""
        if not DARTS_AVAILABLE:
            raise ImportError("Darts library is not available")
            
        try:
            split_point = int(len(ts) * (1 - test_size))
            train, test = ts[:split_point], ts[split_point:]
            return train, test
            
        except Exception as e:
            logger.error(f"Error splitting time series: {e}")
            raise
            
    def get_available_models(self) -> Dict[str, str]:
        """Get list of available forecasting models"""
        if not DARTS_AVAILABLE:
            return {}
            
        models = {
            'naive_seasonal': 'Naive Seasonal (Simple seasonal pattern)',
            'naive_drift': 'Naive Drift (Linear trend)',
            'exponential_smoothing': 'Exponential Smoothing (Holt-Winters)',
            'auto_arima': 'Auto ARIMA (Automatic ARIMA)',
            'prophet': 'Prophet (Facebook Prophet)',
            'linear_regression': 'Linear Regression',
            'random_forest': 'Random Forest',
            'lightgbm': 'LightGBM'
        }
        
        return models
        
    def create_model(self, model_name: str, **kwargs):
        """Create a forecasting model"""
        if not DARTS_AVAILABLE:
            raise ImportError("Darts library is not available")
            
        try:
            if model_name == 'naive_seasonal':
                return NaiveSeasonal(K=kwargs.get('K', 1))
            elif model_name == 'naive_drift':
                return NaiveDrift()
            elif model_name == 'exponential_smoothing':
                return ExponentialSmoothing(
                    trend=kwargs.get('trend', 'add'),
                    seasonal=kwargs.get('seasonal', 'add'),
                    seasonal_periods=kwargs.get('seasonal_periods', None)
                )
            elif model_name == 'auto_arima':
                return AutoARIMA(
                    start_p=kwargs.get('start_p', 0),
                    start_q=kwargs.get('start_q', 0),
                    max_p=kwargs.get('max_p', 5),
                    max_q=kwargs.get('max_q', 5),
                    seasonal=kwargs.get('seasonal', True),
                    stepwise=kwargs.get('stepwise', True),
                    suppress_warnings=True
                )
            elif model_name == 'prophet':
                return Prophet(
                    growth=kwargs.get('growth', 'linear'),
                    seasonality_mode=kwargs.get('seasonality_mode', 'additive'),
                    yearly_seasonality=kwargs.get('yearly_seasonality', 'auto'),
                    weekly_seasonality=kwargs.get('weekly_seasonality', 'auto'),
                    daily_seasonality=kwargs.get('daily_seasonality', 'auto')
                )
            elif model_name == 'linear_regression':
                return LinearRegressionModel(
                    lags=kwargs.get('lags', 14),
                    output_chunk_length=kwargs.get('output_chunk_length', 1)
                )
            elif model_name == 'random_forest':
                return RandomForest(
                    lags=kwargs.get('lags', 14),
                    output_chunk_length=kwargs.get('output_chunk_length', 1),
                    n_estimators=kwargs.get('n_estimators', 100)
                )
            elif model_name == 'lightgbm':
                return LightGBMModel(
                    lags=kwargs.get('lags', 14),
                    output_chunk_length=kwargs.get('output_chunk_length', 1)
                )
            else:
                raise ValueError(f"Unknown model: {model_name}")
                
        except Exception as e:
            logger.error(f"Error creating model {model_name}: {e}")
            raise
            
    def train_model(self, model, train_ts: TimeSeries, model_name: str = None):
        """Train a forecasting model"""
        if not DARTS_AVAILABLE:
            raise ImportError("Darts library is not available")
            
        try:
            logger.info(f"Training model: {model_name or type(model).__name__}")
            model.fit(train_ts)
            
            # Store the trained model
            if model_name:
                self.models[model_name] = model
                
            return model
            
        except Exception as e:
            logger.error(f"Error training model: {e}")
            raise
            
    def forecast(self, model, n_periods: int, series: TimeSeries = None) -> TimeSeries:
        """Generate forecasts using a trained model"""
        if not DARTS_AVAILABLE:
            raise ImportError("Darts library is not available")
            
        try:
            if series is not None:
                # Forecast with input series
                forecast = model.predict(n=n_periods, series=series)
            else:
                # Forecast using the model's internal state
                forecast = model.predict(n=n_periods)
                
            return forecast
            
        except Exception as e:
            logger.error(f"Error generating forecast: {e}")
            raise
            
    def calculate_metrics(self, actual: TimeSeries, predicted: TimeSeries) -> Dict[str, float]:
        """Calculate forecasting metrics"""
        if not DARTS_AVAILABLE:
            raise ImportError("Darts library is not available")
            
        try:
            # Align series for comparison
            if len(actual) != len(predicted):
                min_len = min(len(actual), len(predicted))
                actual = actual[-min_len:]
                predicted = predicted[-min_len:]
            
            metrics = {
                'MAE': float(mae(actual, predicted)),
                'MSE': float(mse(actual, predicted)),
                'RMSE': float(rmse(actual, predicted)),
                'MAPE': float(mape(actual, predicted)),
                'SMAPE': float(smape(actual, predicted)),
                'R2': float(r2_score(actual, predicted))
            }
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error calculating metrics: {e}")
            return {}
          
    def plot_time_series(self, ts: TimeSeries, title: str = "Time Series", width: int = 800, height: int = 400):
        """Plot time series using Plotly"""
        try:
            # Convert TimeSeries to pandas DataFrame using the safe method
            df = self._safe_to_pandas(ts)
            
            fig = go.Figure()
            
            for col in df.columns:
                fig.add_trace(go.Scatter(
                    x=df.index,
                    y=df[col],
                    mode='lines+markers',
                    name=col,
                    line=dict(width=2)
                ))
            
            fig.update_layout(
                title=title,
                xaxis_title="Time",
                yaxis_title="Value",
                width=width,
                height=height,
                hovermode='x unified'
            )
            
            return fig
            
        except Exception as e:
            logger.error(f"Error plotting time series: {e}")
            raise
    
    def plot_forecast(self, historical: TimeSeries, forecast: TimeSeries, 
                     test: TimeSeries = None, title: str = "Forecast", 
                     width: int = 800, height: int = 400):
        """Plot historical data with forecast"""
        try:
            fig = go.Figure()
            
            # Historical data
            hist_df = self._safe_to_pandas(historical)
            fig.add_trace(go.Scatter(
                x=hist_df.index,
                y=hist_df.iloc[:, 0],
                mode='lines+markers',
                name='Historical',
                line=dict(color='blue', width=2)
            ))
            
            # Forecast
            forecast_df = self._safe_to_pandas(forecast)
            fig.add_trace(go.Scatter(
                x=forecast_df.index,
                y=forecast_df.iloc[:, 0],
                mode='lines+markers',
                name='Forecast',
                line=dict(color='red', width=2, dash='dash')
            ))
            
            # Test data (actual future values) if provided
            if test is not None:
                test_df = self._safe_to_pandas(test)
                fig.add_trace(go.Scatter(
                    x=test_df.index,
                    y=test_df.iloc[:, 0],
                    mode='lines+markers',
                    name='Actual',
                    line=dict(color='green', width=2)
                ))
            
            fig.update_layout(
                title=title,
                xaxis_title="Time",
                yaxis_title="Value",
                width=width,
                height=height,
                hovermode='x unified'
            )
            
            return fig
              
        except Exception as e:
            logger.error(f"Error plotting forecast: {e}")
            raise
            
    def decompose_time_series(self, ts: TimeSeries) -> Dict:
        """Decompose time series into trend, seasonal, and residual components with error handling"""
        if not DARTS_AVAILABLE:
            return {}
            
        try:
            # Check if we have enough data points
            if len(ts) < 10:
                logger.warning("Not enough data points for decomposition")
                return {}
            
            # Extract trend and seasonality with error handling
            try:
                trend, seasonal = extract_trend_and_seasonality(ts)
                
                # Calculate residual
                trend_seasonal = trend + seasonal
                residual = ts - trend_seasonal
                
                components = {
                    'trend': trend,
                    'seasonal': seasonal,
                    'residual': residual,
                    'original': ts
                }
                
                return components
                
            except Exception as decomp_error:
                logger.warning(f"Standard decomposition failed: {decomp_error}")                # Try a simpler approach using pandas
                try:
                    df = self._safe_to_pandas(ts)
                    if len(df) < 24:  # Not enough for complex decomposition
                        logger.warning("Not enough data for seasonal decomposition")
                        return {}
                    
                    # Simple rolling mean for trend
                    window = min(12, len(df) // 4)
                    trend_values = df.iloc[:, 0].rolling(window=window, center=True).mean()
                    
                    # Create simple trend TimeSeries
                    trend_df = df.copy()
                    trend_df.iloc[:, 0] = trend_values
                    trend_ts = TimeSeries.from_dataframe(trend_df, freq=ts.freq if hasattr(ts, 'freq') else None)
                    
                    # Simple detrended for seasonal
                    detrended = df.iloc[:, 0] - trend_values
                    seasonal_df = df.copy()
                    seasonal_df.iloc[:, 0] = detrended.fillna(0)
                    seasonal_ts = TimeSeries.from_dataframe(seasonal_df, freq=ts.freq if hasattr(ts, 'freq') else None)
                    
                    # Residual
                    residual_values = df.iloc[:, 0] - trend_values - detrended
                    residual_df = df.copy()
                    residual_df.iloc[:, 0] = residual_values.fillna(0)
                    residual_ts = TimeSeries.from_dataframe(residual_df, freq=ts.freq if hasattr(ts, 'freq') else None)
                    
                    components = {
                        'trend': trend_ts,
                        'seasonal': seasonal_ts,
                        'residual': residual_ts,
                        'original': ts
                    }
                    
                    return components
                    
                except Exception as simple_error:
                    logger.warning(f"Simple decomposition also failed: {simple_error}")
                    return {}
            
        except Exception as e:
            logger.error(f"Error decomposing time series: {e}")
            return {}
            
    def plot_decomposition(self, components: Dict, title: str = "Time Series Decomposition"):
        """Plot time series decomposition"""
        try:
            if not components:
                return None
                
            fig = make_subplots(
                rows=4, cols=1,
                subplot_titles=['Original', 'Trend', 'Seasonal', 'Residual'],
                vertical_spacing=0.05
            )            # Original
            original_df = self._safe_to_pandas(components['original'])
            fig.add_trace(go.Scatter(
                x=original_df.index,
                y=original_df.iloc[:, 0],
                mode='lines',
                name='Original',
                line=dict(color='blue')
            ), row=1, col=1)
            
            # Trend
            trend_df = self._safe_to_pandas(components['trend'])
            fig.add_trace(go.Scatter(
                x=trend_df.index,
                y=trend_df.iloc[:, 0],
                mode='lines',
                name='Trend',
                line=dict(color='red')
            ), row=2, col=1)
            
            # Seasonal
            seasonal_df = self._safe_to_pandas(components['seasonal'])
            fig.add_trace(go.Scatter(
                x=seasonal_df.index,
                y=seasonal_df.iloc[:, 0],
                mode='lines',
                name='Seasonal',
                line=dict(color='green')
            ), row=3, col=1)
            
            # Residual
            residual_df = self._safe_to_pandas(components['residual'])
            fig.add_trace(go.Scatter(
                x=residual_df.index,
                y=residual_df.iloc[:, 0],
                mode='lines',
                name='Residual',
                line=dict(color='orange')
            ), row=4, col=1)
            
            fig.update_layout(
                title=title,
                height=800,
                showlegend=False
            )
            
            return fig
            
        except Exception as e:
            logger.error(f"Error plotting decomposition: {e}")
            return None

    def simple_time_series_analysis(self, df: pd.DataFrame, time_col: str, value_col: str) -> Dict:
        """Simple time series analysis without Darts library"""
        try:
            # Preprocess data
            ts_df = self.preprocess_data(df, time_col, value_col)
            
            if len(ts_df) < 2:
                raise ValueError("Not enough data points for analysis")
            
            # Basic statistics
            values = ts_df[value_col]
            
            analysis = {
                'length': len(ts_df),
                'start_date': ts_df[time_col].min(),
                'end_date': ts_df[time_col].max(),
                'frequency': 'Unknown',
                'has_missing_values': False,
                'components': [value_col],
                'values_min': float(values.min()),
                'values_max': float(values.max()),
                'values_mean': float(values.mean()),
                'values_std': float(values.std()),
                'seasonality': {'is_seasonal': False, 'period': None}
            }
            
            # Simple trend detection
            if len(ts_df) > 10:
                # Calculate correlation with time index
                ts_df_indexed = ts_df.copy()
                ts_df_indexed['time_numeric'] = pd.to_numeric(ts_df_indexed[time_col])
                correlation = ts_df_indexed[value_col].corr(ts_df_indexed['time_numeric'])
                
                analysis['trend'] = {
                    'has_trend': abs(correlation) > 0.5,
                    'direction': 'increasing' if correlation > 0.5 else 'decreasing' if correlation < -0.5 else 'stable',
                    'correlation': float(correlation)
                }
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error in simple time series analysis: {e}")
            raise


def get_time_series_analyzer():
    """Get a TimeSeriesAnalyzer instance"""
    return TimeSeriesAnalyzer()


def is_darts_available() -> bool:
    """Check if Darts library is available"""
    return DARTS_AVAILABLE


def create_simple_forecast_plot(df: pd.DataFrame, time_col: str, value_col: str, 
                               forecast_periods: int = 30) -> Dict:
    """Create a simple forecast plot using basic methods when Darts is not available"""
    try:
        from sklearn.linear_model import LinearRegression
        import plotly.graph_objects as go
        
        # Prepare data
        df_clean = df[[time_col, value_col]].dropna()
        df_clean[time_col] = pd.to_datetime(df_clean[time_col])
        df_clean = df_clean.sort_values(time_col)
        
        # Create numeric time feature
        df_clean['time_numeric'] = pd.to_numeric(df_clean[time_col])
        
        # Fit linear model
        X = df_clean[['time_numeric']].values
        y = df_clean[value_col].values
        
        model = LinearRegression()
        model.fit(X, y)
        
        # Generate future dates
        last_date = df_clean[time_col].max()
        freq = pd.infer_freq(df_clean[time_col]) or 'D'
        future_dates = pd.date_range(start=last_date, periods=forecast_periods + 1, freq=freq)[1:]
        future_numeric = pd.to_numeric(future_dates).values.reshape(-1, 1)
        
        # Make predictions
        predictions = model.predict(future_numeric)
        
        # Create plot
        fig = go.Figure()
        
        # Historical data
        fig.add_trace(go.Scatter(
            x=df_clean[time_col],
            y=df_clean[value_col],
            mode='lines+markers',
            name='Historical',
            line=dict(color='blue', width=2)
        ))
        
        # Forecast
        fig.add_trace(go.Scatter(
            x=future_dates,
            y=predictions,
            mode='lines+markers',
            name='Simple Linear Forecast',
            line=dict(color='red', width=2, dash='dash')
        ))
        
        fig.update_layout(
            title="Simple Time Series Forecast",
            xaxis_title="Time",
            yaxis_title="Value",
            height=500,
            hovermode='x unified'
        )
        return {
            'plot': fig,
            'forecast_values': predictions,
            'forecast_dates': future_dates,
            'model_score': model.score(X, y)
        }
        
    except Exception as e:
        logger.error(f"Error creating simple forecast: {e}")
        raise
