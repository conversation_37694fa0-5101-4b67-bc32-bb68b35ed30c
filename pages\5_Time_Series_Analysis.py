"""
Time Series Analysis and Forecasting Page
This page provides comprehensive time series analysis and forecasting capabilities using the Darts library.
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import logging
from datetime import datetime, timedelta
import json
from typing import Dict, List, Optional

# Import application modules
from app_core.config import init_session_state, get_active_dataset
from app_core.utils.styling import apply_css
from app_core.utils.time_series import get_time_series_analyzer, is_darts_available

# Page configuration
st.set_page_config(
    layout='wide',
    page_title="Time Series Analysis - Analyze and forecast your time series data",
    page_icon="📈"
)

# Initialize session state
init_session_state()

# Apply CSS styling
apply_css()

# Set up logging
logger = logging.getLogger(__name__)

# Initialize time series analyzer
ts_analyzer = get_time_series_analyzer()

# Custom CSS for time series page
st.markdown("""
<style>
.metric-card {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 10px;
    border-left: 4px solid #4e8cff;
    margin: 10px 0;
}

.model-card {
    background-color: #ffffff;
    padding: 20px;
    border-radius: 10px;
    border: 1px solid #e0e0e0;
    margin: 10px 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.forecast-results {
    background-color: #f0f8f0;
    padding: 15px;
    border-radius: 10px;
    border-left: 4px solid #28a745;
    margin: 10px 0;
}

.error-card {
    background-color: #fff5f5;
    padding: 15px;
    border-radius: 10px;
    border-left: 4px solid #dc3545;
    margin: 10px 0;
}
</style>
""", unsafe_allow_html=True)

def display_time_series_info(analysis: Dict):
    """Display time series analysis information"""
    st.subheader("📊 Time Series Analysis")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Length", f"{analysis['length']:,} points")
        
    with col2:
        if analysis.get('frequency'):
            st.metric("Frequency", analysis['frequency'])
        else:
            st.metric("Frequency", "Irregular")
            
    with col3:
        if analysis.get('seasonality', {}).get('is_seasonal'):
            period = analysis['seasonality']['period']
            st.metric("Seasonal", f"Yes (Period: {period})")
        else:
            st.metric("Seasonal", "No")
            
    with col4:
        st.metric("Date Range", f"{analysis['length']} periods")
    
    # Additional statistics
    st.markdown("### 📈 Statistical Summary")
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Minimum", f"{analysis['values_min']:.2f}")
    with col2:
        st.metric("Maximum", f"{analysis['values_max']:.2f}")
    with col3:
        st.metric("Mean", f"{analysis['values_mean']:.2f}")
    with col4:
        st.metric("Std Dev", f"{analysis['values_std']:.2f}")

def display_model_comparison(results: Dict):
    """Display model comparison results"""
    if not results:
        return
        
    st.subheader("🏆 Model Performance Comparison")
    
    # Create comparison dataframe
    comparison_data = []
    for model_name, result in results.items():
        if 'metrics' in result:
            row = {'Model': model_name}
            row.update(result['metrics'])
            comparison_data.append(row)
    
    if comparison_data:
        comparison_df = pd.DataFrame(comparison_data)
        
        # Highlight best performing models
        def highlight_best(s):
            # For error metrics (MAE, MSE, RMSE, MAPE, SMAPE), lower is better
            # For R2, higher is better
            if s.name in ['MAE', 'MSE', 'RMSE', 'MAPE', 'SMAPE']:
                return ['background-color: lightgreen' if v == s.min() else '' for v in s]
            elif s.name == 'R2':
                return ['background-color: lightgreen' if v == s.max() else '' for v in s]
            else:
                return ['' for _ in s]
        
        styled_df = comparison_df.style.apply(highlight_best, subset=[col for col in comparison_df.columns if col != 'Model'])
        st.dataframe(styled_df, use_container_width=True)
        
        # Find best model
        if 'RMSE' in comparison_df.columns:
            best_model_idx = comparison_df['RMSE'].idxmin()
            best_model = comparison_df.loc[best_model_idx, 'Model']
            st.success(f"🥇 Best performing model: **{best_model}** (lowest RMSE)")

# Initialize session state for time series
if 'ts_models_trained' not in st.session_state:
    st.session_state.ts_models_trained = {}
if 'ts_forecasts' not in st.session_state:
    st.session_state.ts_forecasts = {}
if 'ts_selected_columns' not in st.session_state:
    st.session_state.ts_selected_columns = {'time': None, 'value': None}

# Main page content
st.title("📈 Time Series Analysis & Forecasting")

# Check if Darts is available
if not is_darts_available():
    st.warning("""
    ⚠️ **Darts library is not available**
    
    The advanced time series analysis features require the Darts library. 
    However, you can still perform basic time series analysis and simple forecasting.
    
    To install Darts for full functionality:
    
    ```bash
    pip install darts statsmodels scikit-learn
    ```
    
    **Available features without Darts:**
    - ✅ Basic time series visualization
    - ✅ Simple trend analysis  
    - ✅ Basic linear forecasting
    - ❌ Advanced forecasting models (ARIMA, Prophet, etc.)
    - ❌ Seasonal decomposition
    - ❌ Model comparison
    """)
    
    # Continue with limited functionality
    ADVANCED_FEATURES = False
else:
    ADVANCED_FEATURES = True

# Check if we have data
if not st.session_state.has_data or not st.session_state.lazy_datasets:
    st.warning("""
    📊 **No data available for time series analysis**
    
    Please upload a dataset first to perform time series analysis.
    """)
    
    col1, col2 = st.columns(2)
    with col1:
        if st.button("📤 Upload Data", type="primary", use_container_width=True):
            st.switch_page("pages/1_Upload_Data.py")
    with col2:
        if st.button("📥 Import from Database", type="primary", use_container_width=True):
            st.switch_page("pages/3_Database_Connections.py")
    
    st.stop()

# Sidebar for dataset selection and configuration
with st.sidebar:
    st.header("⚙️ Time Series Configuration")
    
    # Dataset selection
    if len(st.session_state.lazy_datasets) > 1:
        st.subheader("📊 Select Dataset")
        dataset_options = list(st.session_state.lazy_datasets.keys())
        selected_dataset = st.selectbox(
            "Choose dataset:",
            dataset_options,
            index=dataset_options.index(st.session_state.active_dataset) if st.session_state.active_dataset in dataset_options else 0,
            key="ts_dataset_selector"
        )
        
        if selected_dataset != st.session_state.active_dataset:
            st.session_state.active_dataset = selected_dataset
            st.rerun()
    
    # Get active dataset
    lazy_dataset = get_active_dataset()
    if not lazy_dataset:
        st.error("No dataset selected")
        st.stop()
    
    # Load sample data for column detection
    sample_df = lazy_dataset.get_sample_cached(1000)
    
    st.subheader("🔍 Column Selection")
    
    # Detect time and numeric columns
    time_columns = ts_analyzer.detect_time_columns(sample_df)
    numeric_columns = ts_analyzer.detect_numeric_columns(sample_df)
    
    # Time column selection
    if time_columns:
        selected_time_col = st.selectbox(
            "📅 Time/Date Column:",
            time_columns,
            index=0,
            help="Select the column containing time/date information"
        )
    else:
        st.error("❌ No time/date columns detected in the dataset")
        st.stop()
    
    # Value column selection
    if numeric_columns:
        # Filter out the time column if it's numeric
        value_column_options = [col for col in numeric_columns if col != selected_time_col]
        if value_column_options:
            selected_value_col = st.selectbox(
                "📊 Value Column:",
                value_column_options,
                index=0,
                help="Select the column containing the values to forecast"
            )
        else:
            st.error("❌ No numeric value columns available (excluding time column)")
            st.stop()
    else:
        st.error("❌ No numeric columns detected in the dataset")
        st.stop()
    
    # Store selected columns
    st.session_state.ts_selected_columns = {
        'time': selected_time_col,
        'value': selected_value_col
    }
    
    # Analysis parameters
    st.subheader("⚙️ Analysis Parameters")
    
    test_size = st.slider(
        "Test Set Size (for validation):",
        min_value=0.1,
        max_value=0.5,
        value=0.2,
        step=0.05,
        help="Portion of data to use for testing model performance"
    )
    
    forecast_periods = st.number_input(
        "Forecast Periods:",
        min_value=1,
        max_value=365,
        value=30,
        help="Number of periods to forecast into the future"
    )

# Main content tabs
tab1, tab2, tab3, tab4 = st.tabs(["📊 Data Analysis", "🤖 Model Training", "📈 Forecasting", "📉 Model Comparison"])

with tab1:
    st.header("📊 Time Series Data Analysis")
    
    try:
        # Load full dataset for analysis
        with st.spinner("Loading data for analysis..."):
            full_df = lazy_dataset.get_full_data()
        
        if ADVANCED_FEATURES:
            # Advanced analysis with Darts
            with st.spinner("Creating time series..."):
                try:
                    ts = ts_analyzer.create_darts_timeseries(
                        full_df, 
                        st.session_state.ts_selected_columns['time'],
                        st.session_state.ts_selected_columns['value']
                    )
                    
                    # Analyze time series
                    analysis = ts_analyzer.analyze_time_series(ts)
                    
                    # Display analysis
                    display_time_series_info(analysis)
                    
                    # Plot original time series
                    st.subheader("📈 Time Series Visualization")
                    fig = ts_analyzer.plot_time_series(
                        ts, 
                        title=f"{st.session_state.ts_selected_columns['value']} over Time",
                        height=500
                    )
                    st.plotly_chart(fig, use_container_width=True)
                    
                    # Time series decomposition
                    st.subheader("🔍 Time Series Decomposition")
                    with st.spinner("Decomposing time series..."):
                        components = ts_analyzer.decompose_time_series(ts)
                    
                    if components:
                        decomp_fig = ts_analyzer.plot_decomposition(components)
                        if decomp_fig:
                            st.plotly_chart(decomp_fig, use_container_width=True)
                        else:
                            st.info("Time series decomposition visualization not available")
                    else:
                        st.info("Time series decomposition not available for this dataset")
                    
                    # Store time series in session state
                    st.session_state.ts_data = ts
                    st.session_state.ts_analysis = analysis
                    
                except Exception as ts_error:
                    st.error(f"❌ Error with advanced time series analysis: {str(ts_error)}")
                    st.info("Falling back to basic analysis...")
                    ADVANCED_FEATURES = False
        
        if not ADVANCED_FEATURES:
            # Basic analysis without Darts
            with st.spinner("Performing basic time series analysis..."):
                analysis = ts_analyzer.simple_time_series_analysis(
                    full_df,
                    st.session_state.ts_selected_columns['time'],
                    st.session_state.ts_selected_columns['value']
                )
                
                # Display basic analysis
                st.subheader("📊 Basic Time Series Analysis")
                
                col1, col2, col3, col4 = st.columns(4)
                with col1:
                    st.metric("Length", f"{analysis['length']:,} points")
                with col2:
                    st.metric("Date Range", f"{analysis['length']} periods")
                with col3:
                    st.metric("Mean", f"{analysis['values_mean']:.2f}")
                with col4:
                    st.metric("Std Dev", f"{analysis['values_std']:.2f}")
                
                # Basic time series plot
                st.subheader("📈 Time Series Visualization")
                time_col = st.session_state.ts_selected_columns['time']
                value_col = st.session_state.ts_selected_columns['value']
                
                # Clean and sort data
                plot_df = full_df[[time_col, value_col]].dropna()
                plot_df[time_col] = pd.to_datetime(plot_df[time_col])
                plot_df = plot_df.sort_values(time_col)
                
                fig = px.line(plot_df, x=time_col, y=value_col, 
                             title=f"{value_col} over Time")
                fig.update_layout(height=500)
                st.plotly_chart(fig, use_container_width=True)
                
                # Trend information
                if 'trend' in analysis:
                    st.subheader("📈 Trend Analysis")
                    trend_info = analysis['trend']
                    if trend_info['has_trend']:
                        st.success(f"📈 Trend detected: {trend_info['direction']} (correlation: {trend_info['correlation']:.3f})")
                    else:
                        st.info("📊 No clear trend detected")
                
                # Store basic analysis
                st.session_state.ts_analysis = analysis
                st.session_state.ts_basic_mode = True
        
    except Exception as e:
        st.error(f"❌ Error analyzing time series: {str(e)}")
        logger.error(f"Time series analysis error: {e}")
        st.info("💡 **Troubleshooting tips:**")
        st.markdown("""
        - Ensure your time column contains valid dates/timestamps
        - Check that your value column contains numeric data
        - Verify you have sufficient data points (at least 10-20)
        - Make sure there are no extreme duplicates or data quality issues
        """)

with tab2:
    st.header("🤖 Model Training")
    
    if not ADVANCED_FEATURES:
        st.warning("""
        🤖 **Advanced Model Training Not Available**
        
        Model training requires the Darts library. Please install it for access to:
        
        - 📊 Multiple forecasting models (ARIMA, Prophet, etc.)
        - 🏆 Model performance comparison
        - 📈 Advanced forecasting capabilities
        
        ```bash
        pip install darts statsmodels scikit-learn
        ```
        
        You can still use basic linear forecasting in the 'Forecasting' tab.
        """)
    elif 'ts_data' not in st.session_state:
        st.warning("Please analyze the data in the 'Data Analysis' tab first.")
    else:
        ts = st.session_state.ts_data
        
        # Split data
        train_ts, test_ts = ts_analyzer.split_train_test(ts, test_size=test_size)
        
        st.info(f"📊 Training set: {len(train_ts)} points | Test set: {len(test_ts)} points")
        
        # Model selection
        available_models = ts_analyzer.get_available_models()
        
        st.subheader("🔧 Select Models to Train")
        
        selected_models = st.multiselect(
            "Choose forecasting models:",
            list(available_models.keys()),
            default=['naive_seasonal', 'exponential_smoothing', 'auto_arima'],
            format_func=lambda x: available_models[x]
        )
        
        if selected_models:
            # Model parameters (basic)
            st.subheader("⚙️ Model Parameters")
            
            model_params = {}
            for model_name in selected_models:
                with st.expander(f"Configure {available_models[model_name]}"):
                    if model_name == 'exponential_smoothing':
                        model_params[model_name] = {
                            'trend': st.selectbox(f"Trend ({model_name}):", ['add', 'mul', None], index=0, key=f"trend_{model_name}"),
                            'seasonal': st.selectbox(f"Seasonal ({model_name}):", ['add', 'mul', None], index=0, key=f"seasonal_{model_name}"),
                            'seasonal_periods': st.number_input(f"Seasonal Periods ({model_name}):", min_value=1, max_value=365, value=12, key=f"periods_{model_name}")
                        }
                    elif model_name == 'auto_arima':
                        model_params[model_name] = {
                            'max_p': st.number_input(f"Max P ({model_name}):", min_value=1, max_value=10, value=5, key=f"max_p_{model_name}"),
                            'max_q': st.number_input(f"Max Q ({model_name}):", min_value=1, max_value=10, value=5, key=f"max_q_{model_name}"),
                            'seasonal': st.checkbox(f"Seasonal ({model_name}):", value=True, key=f"seasonal_{model_name}")
                        }
                    elif model_name in ['linear_regression', 'random_forest', 'lightgbm']:
                        model_params[model_name] = {
                            'lags': st.number_input(f"Lags ({model_name}):", min_value=1, max_value=50, value=14, key=f"lags_{model_name}")
                        }
                    else:
                        model_params[model_name] = {}
            
            # Train models
            if st.button("🚀 Train Selected Models", type="primary"):
                results = {}
                
                progress_bar = st.progress(0)
                status_text = st.empty()
                
                for i, model_name in enumerate(selected_models):
                    try:
                        status_text.text(f"Training {available_models[model_name]}...")
                        
                        # Create and train model
                        model = ts_analyzer.create_model(model_name, **model_params.get(model_name, {}))
                        trained_model = ts_analyzer.train_model(model, train_ts, model_name)
                        
                        # Generate predictions on test set
                        if len(test_ts) > 0:
                            forecast = ts_analyzer.forecast(trained_model, len(test_ts), train_ts)
                            metrics = ts_analyzer.calculate_metrics(test_ts, forecast)
                        else:
                            forecast = None
                            metrics = {}
                        
                        results[model_name] = {
                            'model': trained_model,
                            'forecast': forecast,
                            'metrics': metrics,
                            'model_display_name': available_models[model_name]
                        }
                        
                        progress_bar.progress((i + 1) / len(selected_models))
                        
                    except Exception as e:
                        st.error(f"❌ Error training {available_models[model_name]}: {str(e)}")
                        logger.error(f"Model training error for {model_name}: {e}")
                
                status_text.text("Training completed!")
                
                # Store results
                st.session_state.ts_models_trained = results
                st.session_state.ts_train = train_ts
                st.session_state.ts_test = test_ts
                
                # Display results
                if results:
                    st.success(f"✅ Successfully trained {len(results)} models!")
                    display_model_comparison(results)

with tab3:
    st.header("📈 Forecasting")
    
    # Check if we have analysis results
    if 'ts_analysis' not in st.session_state:
        st.warning("Please analyze the data in the 'Data Analysis' tab first.")
    else:
        if ADVANCED_FEATURES and 'ts_models_trained' in st.session_state and st.session_state.ts_models_trained:
            # Advanced forecasting with trained models
            results = st.session_state.ts_models_trained
            train_ts = st.session_state.ts_train
            test_ts = st.session_state.ts_test
            
            # Model selection for forecasting
            model_options = list(results.keys())
            selected_forecast_model = st.selectbox(
                "Select model for forecasting:",
                model_options,
                format_func=lambda x: results[x]['model_display_name']
            )
            
            if selected_forecast_model:
                model_result = results[selected_forecast_model]
                model = model_result['model']
                
                # Generate forecast
                if st.button("🔮 Generate Advanced Forecast", type="primary"):
                    try:
                        with st.spinner("Generating forecast..."):
                            # Generate forecast
                            forecast = ts_analyzer.forecast(model, forecast_periods, train_ts)
                            
                            # Plot results
                            st.subheader("📊 Forecast Results")
                            
                            # Create forecast plot
                            forecast_fig = ts_analyzer.plot_forecast(
                                train_ts, 
                                forecast, 
                                test_ts if len(test_ts) > 0 else None,
                                title=f"Forecast using {model_result['model_display_name']}",
                                height=600
                            )
                            st.plotly_chart(forecast_fig, use_container_width=True)
                              # Display forecast values
                            st.subheader("📋 Forecast Values")
                            forecast_df = forecast.pd_dataframe() if hasattr(forecast, 'pd_dataframe') else forecast.to_pandas()
                            forecast_df.columns = ['Forecast']
                            forecast_df.index.name = 'Date'
                            st.dataframe(forecast_df.round(4), use_container_width=True)
                            
                            # Download forecast
                            csv = forecast_df.to_csv()
                            st.download_button(
                                label="💾 Download Forecast as CSV",
                                data=csv,
                                file_name=f"forecast_{selected_forecast_model}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                                mime="text/csv"
                            )
                            
                            # Display model performance on test set
                            if 'metrics' in model_result and model_result['metrics']:
                                st.subheader("📊 Model Performance (on test set)")
                                metrics_df = pd.DataFrame([model_result['metrics']])
                                st.dataframe(metrics_df.round(4), use_container_width=True)
                            
                            # Store forecast
                            st.session_state.ts_forecasts[selected_forecast_model] = forecast
                            
                    except Exception as e:
                        st.error(f"❌ Error generating forecast: {str(e)}")
                        logger.error(f"Forecasting error: {e}")
        else:
            # Basic forecasting without advanced models
            st.subheader("📈 Simple Linear Forecasting")
            st.info("💡 Using basic linear regression for forecasting. Install Darts for advanced models.")
            
            if st.button("🔮 Generate Simple Forecast", type="primary"):
                try:
                    from app_core.utils.time_series import create_simple_forecast_plot
                    
                    with st.spinner("Generating simple forecast..."):
                        # Load data
                        full_df = lazy_dataset.get_full_data()
                        
                        # Create simple forecast
                        forecast_result = create_simple_forecast_plot(
                            full_df,
                            st.session_state.ts_selected_columns['time'],
                            st.session_state.ts_selected_columns['value'],
                            forecast_periods
                        )
                        
                        # Display results
                        st.subheader("📊 Simple Forecast Results")
                        st.plotly_chart(forecast_result['plot'], use_container_width=True)
                        
                        # Display model performance
                        st.metric("Model R² Score", f"{forecast_result['model_score']:.4f}")
                        
                        # Create forecast dataframe
                        forecast_df = pd.DataFrame({
                            'Date': forecast_result['forecast_dates'],
                            'Forecast': forecast_result['forecast_values']
                        })
                        
                        st.subheader("📋 Forecast Values")
                        st.dataframe(forecast_df.round(4), use_container_width=True)
                        
                        # Download forecast
                        csv = forecast_df.to_csv(index=False)
                        st.download_button(
                            label="💾 Download Simple Forecast as CSV",
                            data=csv,
                            file_name=f"simple_forecast_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                            mime="text/csv"
                        )
                        
                except Exception as e:
                    st.error(f"❌ Error generating simple forecast: {str(e)}")
                    logger.error(f"Simple forecasting error: {e}")
            
            # Show limitations and suggestions
            st.markdown("---")
            st.subheader("🚀 Upgrade to Advanced Forecasting")
            st.markdown("""
            **Simple Linear Forecasting Limitations:**
            - Only captures linear trends
            - Cannot model seasonality
            - No confidence intervals
            - Limited accuracy for complex patterns
            
            **Install Darts for Advanced Features:**
            ```bash
            pip install darts statsmodels scikit-learn
            ```
            
            **Advanced Models Available:**
            - 📈 ARIMA (Auto ARIMA)
            - 🔮 Prophet (Facebook Prophet) 
            - 📊 Exponential Smoothing
            - 🤖 Machine Learning Models (Random Forest, LightGBM)
            - 📉 Model Performance Comparison
            - 🔍 Seasonal Decomposition
            """)

with tab4:
    st.header("📉 Model Comparison")
    
    if not ADVANCED_FEATURES:
        st.warning("""
        📉 **Model Comparison Not Available**
        
        Model comparison requires the Darts library and trained models.
        Install Darts to access advanced model comparison features:
        
        ```bash
        pip install darts statsmodels scikit-learn
        ```
        
        **Available with Darts:**
        - 🏆 Performance metrics comparison
        - 📊 Visual model predictions comparison
        - 📈 Best model recommendations
        """)
    elif 'ts_models_trained' not in st.session_state or not st.session_state.ts_models_trained:
        st.warning("Please train models in the 'Model Training' tab first.")
    else:
        results = st.session_state.ts_models_trained
        
        # Display detailed comparison
        display_model_comparison(results)
        
        # Visualization of model predictions
        st.subheader("📊 Model Predictions Comparison")
        
        if st.session_state.get('ts_test') is not None and len(st.session_state.ts_test) > 0:
            train_ts = st.session_state.ts_train
            test_ts = st.session_state.ts_test
            
            # Create comparison plot
            fig = go.Figure()
            
            # Add historical data
            train_df = train_ts.pd_dataframe()
            fig.add_trace(go.Scatter(
                x=train_df.index,
                y=train_df.iloc[:, 0],
                mode='lines',
                name='Training Data',
                line=dict(color='blue', width=2)
            ))
            
            # Add actual test data
            test_df = test_ts.pd_dataframe()
            fig.add_trace(go.Scatter(
                x=test_df.index,
                y=test_df.iloc[:, 0],
                mode='lines+markers',
                name='Actual (Test)',
                line=dict(color='green', width=2)
            ))
            
            # Add model predictions
            colors = ['red', 'orange', 'purple', 'brown', 'pink']
            for i, (model_name, result) in enumerate(results.items()):
                if result.get('forecast') is not None:
                    forecast_df = result['forecast'].pd_dataframe()
                    fig.add_trace(go.Scatter(
                        x=forecast_df.index,
                        y=forecast_df.iloc[:, 0],
                        mode='lines',
                        name=f"{result['model_display_name']} Prediction",
                        line=dict(color=colors[i % len(colors)], width=2, dash='dash')
                    ))
            
            fig.update_layout(
                title="Model Predictions Comparison",
                xaxis_title="Time",
                yaxis_title="Value",
                height=600,
                hovermode='x unified'
            )
            
            st.plotly_chart(fig, use_container_width=True)
        else:
            st.info("No test data available for comparison visualization.")

# Additional information in sidebar
with st.sidebar:
    st.markdown("---")
    st.subheader("ℹ️ About Time Series Analysis")
    st.markdown("""
    **Features:**
    - 📊 Time series visualization and analysis
    - 🔍 Automatic seasonality detection
    - 📈 Multiple forecasting models
    - 🏆 Model performance comparison
    - 📉 Time series decomposition
    - 💾 Export forecasts to CSV
    
    **Supported Models:**
    - Naive methods (Seasonal, Drift)
    - Exponential Smoothing (Holt-Winters)
    - ARIMA (Auto ARIMA)
    - Prophet (Facebook Prophet)
    - Machine Learning (Random Forest, LightGBM)
    """)
    
    if st.session_state.get('ts_analysis'):
        st.markdown("---")
        st.subheader("📊 Current Dataset Info")
        analysis = st.session_state.ts_analysis
        st.markdown(f"""
        - **Length:** {analysis['length']:,} points
        - **Frequency:** {analysis.get('frequency', 'Irregular')}
        - **Seasonal:** {'Yes' if analysis.get('seasonality', {}).get('is_seasonal') else 'No'}
        - **Date Range:** {analysis['start_date']} to {analysis['end_date']}
        """)
