import uuid
import time
import pandas as pd
import streamlit as st
import plotly.graph_objs as go
from app_core.utils.logging import log_message
import matplotlib.pyplot as plt
import matplotlib.figure
import io
import base64

# === Dispatch map for simple types ===
TYPE_HANDLERS = {
    'dataframe': lambda df, uid: _display_dataframe(df, uid),
    'figure':    lambda fig, uid: _display_figure(fig, uid),
    'plot':      lambda fig, uid: _display_figure(fig, uid),
    'string':    lambda txt,uid: st.markdown(txt),
    'text':      lambda txt,uid: st.markdown(txt),
    'html':      lambda h, uid: st.components.v1.html(h, height=500, scrolling=True),
    'code':      lambda c, uid: st.code(c),
    'number':    lambda n, uid: st.markdown(str(n)),
}

def _add_result_styling():
    """Add CSS styling for result sections"""
    st.markdown("""
    <style>
    .result-section {
        margin: 20px 0;
    }
    .result-section-header {
        background-color: #4e8cff;
        color: white;
        padding: 8px 15px;
        border-radius: 5px;
        font-weight: bold;
        margin-bottom: 10px;
    }
    </style>
    """, unsafe_allow_html=True)

def display_result(result, unique_id=None):
    """Master entry point – unwrap lists/MultipleResponse and dispatch each item."""
    _add_result_styling()
    unique_id = unique_id or f"result_{uuid.uuid4().hex[:8]}"
    log_message(f"display_result: type={type(result)}")

    items = _unwrap_multiple_response(result) \
         or _unwrap_list_of_dicts(result)

    if items:
        for i, item in enumerate(items):
            uid = f"{unique_id}_{i}"
            if desc := item.get('description'):
                st.subheader(desc)
            _render_item(item['type'], item['value'], uid)
        return

    # single‐value fallback
    t = getattr(result, 'type', None) or _infer_type(result)
    v = getattr(result, 'value', result)
    _render_item(t, v, unique_id)


def _unwrap_multiple_response(obj):
    if 'MultipleResponse' in str(type(obj)) and hasattr(obj, 'value') and isinstance(obj.value, list):
        log_message(f"Unwrapping MultipleResponse ({len(obj.value)} items)")
        return [ _normalize_item(it) for it in obj.value ]
    return None

def _unwrap_list_of_dicts(obj):
    if isinstance(obj, list) and all(isinstance(it, dict) and 'type' in it and 'value' in it for it in obj):
        log_message(f"Unwrapping list of dicts ({len(obj)} items)")
        return [ _normalize_item(it) for it in obj ]
    return None

def _normalize_item(item):
    """Ensure we always have a dict with keys 'type','value','description'."""
    return {
        'type': item.get('type'),
        'value': item.get('value'),
        'description': item.get('description', None)
    }

def _render_item(t, v, uid):
    log_message(f"_render_item: type={t} uid={uid}")
    handler = TYPE_HANDLERS.get(t)
    if handler:
        return handler(v, uid)
    # otherwise fallback by actual Python type
    if isinstance(v, pd.DataFrame):
        return _display_dataframe(v, uid)
    if isinstance(v, pd.Series):
        return _display_series(v, uid)
    if isinstance(v, (int, float)):
        return st.markdown(str(v))
    if isinstance(v, str):
        return st.markdown(v)
    # Check for matplotlib objects
    if hasattr(v, 'savefig') or str(type(v)).find('matplotlib') != -1 or hasattr(v, 'gcf'):
        return _display_figure(v, uid)
    if 'plotly.graph_objs' in str(type(v)) or hasattr(v, 'to_dict'):
        return _display_figure(v, uid)
    if isinstance(v, (list, dict)):
        return st.json(v)
    # last resort
    st.write(v)


def _display_dataframe(df: pd.DataFrame, uid):
    log_message(f"_display_dataframe shape={df.shape} uid={uid}")
    if df.empty:
        return st.warning("Empty DataFrame")
    height = min(500, 35 + 35 * min(df.shape[0], 10))
    st.dataframe(df, height=height, use_container_width=True)
    csv = df.to_csv(index=False)
    ts = int(time.time())
    st.download_button(
        label="Download CSV",
        data=csv,
        file_name=f"data_{ts}.csv",
        mime="text/csv",
        key=f"dl_{uid}"
    )

def _display_series(sr: pd.Series, uid):
    log_message(f"_display_series length={len(sr)} uid={uid}")
    _display_dataframe(pd.DataFrame(sr), uid)

def _display_figure(fig, uid, height=500, simple=True):
    """Display matplotlib or plotly figures"""
    log_message(f"_display_figure: type={type(fig)} uid={uid}")
    
    # Handle matplotlib figures/pyplot objects
    if hasattr(fig, 'savefig') or str(type(fig)).find('matplotlib') != -1:
        try:
            # If it's a pyplot module (plt), get the current figure
            if hasattr(fig, 'gcf'):
                actual_fig = fig.gcf()
            else:
                actual_fig = fig
            
            # Save figure to bytes buffer
            buf = io.BytesIO()
            actual_fig.savefig(buf, format='png', dpi=150, bbox_inches='tight')
            buf.seek(0)
            
            # Display in streamlit
            st.image(buf, use_container_width=True)
            
            # Clear the figure to prevent memory issues
            if hasattr(fig, 'clf'):
                fig.clf()
            elif hasattr(actual_fig, 'clf'):
                actual_fig.clf()
                
            return
        except Exception as e:
            log_message(f"Error displaying matplotlib figure: {str(e)}")
            st.error(f"Error displaying matplotlib figure: {str(e)}")
            return
    
    # Handle plotly figures (existing code)
    if not isinstance(fig, go.Figure):
        try:
            fig = go.Figure(fig.to_dict())
        except:
            fig = go.Figure(fig)
    # styling
    tmpl = dict(template="plotly_white", margin=dict(l=40,r=40,t=50,b=40), height=height)
    if not simple:
        tmpl.update(legend=dict(orientation="h",yanchor="bottom",y=1.02,xanchor="right",x=1),
                    plot_bgcolor='white', paper_bgcolor='white',
                    font=dict(family="Arial",size=12), autosize=True)
        fig.update_xaxes(showgrid=True,gridcolor='#f0f0f0')
        fig.update_yaxes(showgrid=True,gridcolor='#f0f0f0')
    fig.update_layout(**tmpl)
    st.plotly_chart(fig, use_container_width=True, key=f"fig_{uid}")

def _infer_type(v):
    if isinstance(v, pd.DataFrame): return 'dataframe'
    if isinstance(v, pd.Series):    return 'dataframe'
    if isinstance(v, (int,float)):   return 'number'
    if isinstance(v, str):           return 'string'
    return None
