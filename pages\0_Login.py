# Login Page
# Provides user authentication interface

import streamlit as st
import sys
import os

# Page configuration
st.set_page_config(
    page_title="Login",
    page_icon="🔐",
    layout="centered",
)

# Add the parent directory to the path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import required modules
from app_core.config import init_session_state
from app_core.auth.auth_config import AuthConfig
from app_core.utils.styling import apply_css

# Initialize session state (without requiring authentication)
if 'initialized' not in st.session_state:
    init_session_state()

# Apply styling
apply_css()

# Initialize authentication
auth = AuthConfig()

# Custom CSS for login page
st.markdown("""
<style>
.login-container {
    max-width: 400px;
    margin: 0 auto;
    padding: 2rem;
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border: 1px solid #e0e0e0;
}

.login-header {
    text-align: center;
    margin-bottom: 2rem;
}

.login-header h1 {
    color: #262730;
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.login-header p {
    color: #666;
    font-size: 1rem;
}

.stForm {
    background: transparent;
}

.login-footer {
    text-align: center;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #e0e0e0;
    color: #666;
    font-size: 0.9rem;
}
</style>
""", unsafe_allow_html=True)

# Check if already authenticated
if auth.is_authenticated():
    st.success("✅ You are already logged in!")
    
    col1, col2, col3 = st.columns([1, 2, 1])
    with col2:
        if st.button("🗄️ Go to Database Connections", use_container_width=True, type="primary"):
            st.switch_page("pages/3_Database_Connections.py")
        
        if st.button("🏠 Go to Home", use_container_width=True):
            st.switch_page("Welcome.py")
        
        if st.button("🚪 Logout", use_container_width=True):
            auth.logout()
            st.rerun()
else:
    
    # Render the actual login form
    auth.render_login_form()