/* Main application styles */

/* Hide the "app" text in the sidebar */
section[data-testid="stSidebar"] div.element-container:first-child {
    display: none;
}

/* Modern table styling */
table {
    border-collapse: collapse;
    margin: 25px 0;
    font-size: 13px;
    font-family: Arial, sans-serif;
    width: 100%;
    border: 1px solid #eee;
}

table thead tr {
    background-color: var(--gray-light, #f7f7f9);
    color: var(--text-color, #333);
    text-align: left;
    font-weight: normal;
}

table th,
table td {
    padding: 8px 12px;
    border: 1px solid #eee;
}

table tbody tr {
    border-bottom: 1px solid #eee;
}

table tbody tr:hover {
    background-color: #f9f9fb;
}

/* Improve dataframe styling */
[data-testid="stDataFrame"] {
    border: 1px solid #eee;
}

/* Style Streamlit's native tables */
div[data-testid="stTable"] table {
    border: 1px solid #eee;
}

div[data-testid="stTable"] thead tr th {
    background-color: var(--gray-light, #f7f7f9);
    color: var(--text-color, #333);
    font-weight: normal;
}

div[data-testid="stTable"] tbody tr td {
    border: 1px solid #eee;
}

/* Universal button styling - comprehensive fix for text color issues */
div[data-testid="stButton"] button {
    background-color: var(--secondary-color, #4e8cff) !important;
    color: white !important;
    font-weight: 500 !important;
    border-radius: 5px !important;
    border: none !important;
    padding: 0.5rem 1rem !important;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1) !important;
    transition: all 0.3s ease !important;
    cursor: pointer !important;
}

div[data-testid="stButton"] button:hover {
    background-color: var(--secondary-hover-color, #3a78f0) !important;
    color: white !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
}

div[data-testid="stButton"] button:active {
    color: white !important;
    transform: translateY(0px) !important;
}

div[data-testid="stButton"] button:focus {
    color: white !important;
    outline: none !important;
    box-shadow: 0 0 0 2px rgba(78, 140, 255, 0.3) !important;
}

/* Disabled button styling */
div[data-testid="stButton"] button:disabled {
    background-color: #cccccc !important;
    color: #666666 !important;
    cursor: not-allowed !important;
    transform: none !important;
    box-shadow: none !important;
}

/* Primary button styling */
div[data-testid="stButton"] button[kind="primary"] {
    background-color: var(--secondary-color, #4e8cff) !important;
    color: white !important;
    font-weight: 600 !important;
}

div[data-testid="stButton"] button[kind="primary"]:hover {
    background-color: var(--secondary-hover-color, #3a78f0) !important;
    color: white !important;
}

/* Secondary button styling */
div[data-testid="stButton"] button[kind="secondary"] {
    background-color: #f0f2f6 !important;
    color: #31333F !important;
    border: 1px solid #D1D5DB !important;
}

div[data-testid="stButton"] button[kind="secondary"]:hover {
    background-color: #D1D5DB !important;
    color: #31333F !important;
}

/* Sidebar button styling */
section[data-testid="stSidebar"] div[data-testid="stButton"] button {
    background-color: var(--secondary-color, #4e8cff) !important;
    color: white !important;
}

section[data-testid="stSidebar"] div[data-testid="stButton"] button:hover {
    background-color: var(--secondary-hover-color, #3a78f0) !important;
    color: white !important;
}

/* Form submit button styling */
div[data-testid="stForm"] div[data-testid="stButton"] button {
    background-color: var(--secondary-color, #4e8cff) !important;
    color: white !important;
}

div[data-testid="stForm"] div[data-testid="stButton"] button:hover {
    background-color: var(--secondary-hover-color, #3a78f0) !important;
    color: white !important;
}

/* Dataset view toggle button styling */
div[data-testid="stButton"] button.view-dataset-btn {
    background-color: var(--secondary-color, #4e8cff) !important;
    color: white !important;
    font-weight: 500 !important;
    border-radius: 5px;
    border: none !important;
    padding: 0.5rem 1rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1) !important;
    transition: all 0.3s ease;
    width: 100%;
}

div[data-testid="stButton"] button.view-dataset-btn:hover {
    background-color: var(--secondary-hover-color, #3a78f0) !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
}

/* Make expanders more prominent */
.streamlit-expanderHeader {
    font-size: 1.1em;
    background-color: var(--gray-light, #f0f2f6);
    border-radius: 5px;
    padding: 10px !important;
    border-left: 5px solid var(--secondary-color, #4e8cff);
}

/* Add hover effect to expanders */
.streamlit-expanderHeader:hover {
    background-color: #e6e9ef;
}

/* Style the tabs */
.stTabs [data-baseweb="tab-list"] {
    gap: 24px;
}

.stTabs [data-baseweb="tab"] {
    height: 50px;
    white-space: pre-wrap;
    border-radius: 4px 4px 0 0;
    padding: 10px 16px;
    font-weight: 500;
}

/* Style the active tab */
.stTabs [aria-selected="true"] {
    background-color: var(--gray-light, #f0f2f6);
    border-bottom: 2px solid var(--secondary-color, #4e8cff);
}

/* Make PyGWalker component take up more space */
iframe {
    height: 600px !important;
    width: 100% !important;
    border: none !important;
}

/* Style the chat container */
[data-testid="stChatMessageContent"] {
    border-radius: 8px;
    padding: 10px;
}

/* Floating scroll button styles */
.floating-scroll-button {
    position: fixed;
    bottom: 120px;
    right: 100px;
    width: 50px;
    height: 50px;
    background-color: var(--secondary-color, #4e8cff);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    z-index: 9999;
    border: none;
    font-size: 24px;
    transition: all 0.3s ease;
}

.floating-scroll-button:hover {
    background-color: var(--secondary-hover-color, #3a78f0);
    transform: scale(1.05);
}

/* Add padding to the bottom of the page to prevent content from being hidden */
.main .block-container {
    padding-bottom: 80px;
}

/* Result section styling */
.result-section {
    margin: 20px 0;
}
.result-section-header {
    background-color: var(--secondary-color, #4e8cff);
    color: white;
    padding: 8px 15px;
    border-radius: 5px;
    font-weight: bold;
    margin-bottom: 10px;
}

/* Welcome page styling */
.welcome-container {
    background-color: var(--gray-light, #f8f9fa);
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
}
